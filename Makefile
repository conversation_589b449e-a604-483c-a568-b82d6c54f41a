# Media Convert Service - Makefile

.PHONY: help setup up down clean logs worker test lint format db-test db-migrate db-reset

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make setup    - Initial setup"
	@echo "  make up       - Start RabbitMQ"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make format   - Format code"
	@echo "  make logs     - View logs"
	@echo "  make clean    - Clean up"
	@echo ""
	@echo "Database Commands:"
	@echo "  make db-test    - Test database connection"
	@echo "  make db-migrate - Run database migrations"
	@echo "  make db-reset   - Reset database (DANGER)"
	@echo ""
	@echo "Conversion Commands:"
	@echo "  make test-conversion - Test video conversion"
	@echo "  make list-conversions - List recent conversions"
	@echo "  make status JOB_ID=<id> - Check conversion status"

# Setup
setup:
	@mkdir -p logs temp
	@if [ ! -f .env ]; then cp .env.example .env; fi

# Services
up:
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

exec:
	@docker-compose exec -it media-convert-worker /bin/bash

down:
	@docker-compose --profile worker down -v

# Testing
test:
	@docker-compose exec media-convert-worker pytest -v

# Code quality
lint:
	@docker-compose exec media-convert-worker ruff check app/ --fix

format:
	@docker-compose exec media-convert-worker ruff format app/

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Cleanup
clean:
	@docker-compose --profile worker down -v
	@docker system prune -f

# Build
build:
	@docker-compose build media-convert-worker

# Database commands
db-test:
	@docker-compose exec media-convert-worker python scripts/db-commands.py test

db-migrate:
	@docker-compose exec media-convert-worker /app/scripts/run-migrations.sh

db-reset:
	@echo "⚠️ This will DELETE ALL DATA in the database!"
	@read -p "Type 'YES' to confirm: " confirm && [ "$$confirm" = "YES" ] || exit 1
	@docker-compose exec media-convert-worker python scripts/db-commands.py drop
	@docker-compose exec media-convert-worker /app/scripts/run-migrations.sh

# Conversion commands
test-conversion:
	@docker exec media-convert-worker ./run-local.sh

list-conversions:
	@docker-compose exec media-convert-worker python scripts/check-conversions.py list

status:
	@if [ -z "$(JOB_ID)" ]; then echo "Usage: make status JOB_ID=<job_id>"; exit 1; fi
	@docker-compose exec media-convert-worker python scripts/check-conversions.py status $(JOB_ID)
