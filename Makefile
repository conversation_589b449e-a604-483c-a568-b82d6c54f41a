# Media Convert Service - Makefile

.PHONY: help setup up down clean logs worker

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make setup    - Initial setup"
	@echo "  make up       - Start RabbitMQ"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make logs     - View logs"
	@echo "  make clean    - Clean up"

# Setup
setup:
	@mkdir -p logs temp
	@if [ ! -f .env ]; then cp .env.example .env; fi

# Services
up:
	@docker-compose up -d rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down



# Logs
logs:
	@docker-compose logs -f

# Cleanup
clean:
	@docker-compose --profile worker down -v
	@docker system prune -f

# Build
build:
	@docker-compose build media-convert-worker
