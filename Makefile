# Media Convert Service - Development Makefile
# 
# Usage:
#   make help          - Show this help message
#   make setup         - Initial setup (create .env, directories)
#   make up            - Start core services (RabbitMQ + Redis)
#   make worker        - Start worker in development mode
#   make down          - Stop all services
#   make clean         - Clean up containers and volumes
#   make logs          - Show logs from all services
#   make test          - Run tests
#   make lint          - Run code linting
#   make format        - Format code

.PHONY: help setup up down clean logs test lint format worker s3-local status

# Colors for output
YELLOW := \033[1;33m
GREEN := \033[0;32m
RED := \033[0;31m
NC := \033[0m

# Default target
help:
	@echo "$(GREEN)Media Convert Service - Development Commands$(NC)"
	@echo ""
	@echo "$(YELLOW)Setup:$(NC)"
	@echo "  make setup         - Initial project setup"
	@echo "  make up            - Start RabbitMQ service"
	@echo "  make worker        - Start worker container"
	@echo ""
	@echo "$(YELLOW)Development:$(NC)"
	@echo "  make dev           - Start services + worker (full dev environment)"
	@echo "  make s3-local      - Start with LocalStack S3 simulation"
	@echo "  make shell         - Open shell in worker container"
	@echo ""
	@echo "$(YELLOW)Monitoring:$(NC)"
	@echo "  make status        - Show status of all services"
	@echo "  make logs          - Show logs from all services"
	@echo "  make logs-worker   - Show worker logs only"
	@echo "  make rabbitmq-ui   - Open RabbitMQ Management UI"
	@echo ""
	@echo "$(YELLOW)Testing & Quality:$(NC)"
	@echo "  make test-env      - Test environment connectivity"
	@echo "  make test-celery   - Test Celery task execution"
	@echo "  make test          - Run unit tests"
	@echo "  make lint          - Run code linting"
	@echo "  make format        - Format code with black"
	@echo ""
	@echo "$(YELLOW)Cleanup:$(NC)"
	@echo "  make down          - Stop all services"
	@echo "  make clean         - Remove containers and volumes"
	@echo "  make reset         - Complete reset (clean + setup)"

# Initial setup
setup:
	@echo "$(GREEN)🚀 Setting up Media Convert development environment...$(NC)"
	@mkdir -p logs temp
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(YELLOW)📝 Created .env file from template. Please review and update it.$(NC)"; \
	fi
	@echo "$(GREEN)✅ Setup complete!$(NC)"

# Start core services
up:
	@echo "$(GREEN)🐰 Starting RabbitMQ service...$(NC)"
	@docker-compose up -d rabbitmq
	@echo "$(GREEN)⏳ Waiting for RabbitMQ to be ready...$(NC)"
	@sleep 15
	@$(MAKE) status

# Start worker
worker:
	@echo "$(GREEN)👷 Starting worker container...$(NC)"
	@docker-compose --profile worker up -d media-convert-worker

# Full development environment
dev: up worker
	@echo "$(GREEN)🎉 Full development environment is running!$(NC)"
	@$(MAKE) status

# Start with LocalStack S3
s3-local:
	@echo "$(GREEN)☁️ Starting with LocalStack S3 simulation...$(NC)"
	@docker-compose --profile s3-local up -d localstack
	@docker-compose up -d rabbitmq
	@sleep 20
	@echo "$(GREEN)📦 Creating S3 bucket...$(NC)"
	@docker run --rm --network media-convert_media-convert-network \
		-e AWS_ACCESS_KEY_ID=test \
		-e AWS_SECRET_ACCESS_KEY=test \
		-e AWS_DEFAULT_REGION=us-east-1 \
		amazon/aws-cli --endpoint-url=http://localstack:4566 \
		s3 mb s3://media-convert-dev || true
	@echo "$(GREEN)✅ LocalStack S3 ready!$(NC)"

# Show status
status:
	@echo "$(GREEN)📊 Service Status:$(NC)"
	@docker-compose ps
	@echo ""
	@echo "$(GREEN)🔗 Service URLs:$(NC)"
	@echo "  🐰 RabbitMQ Management: http://localhost:15672 (admin/admin123)"
	@if docker-compose ps | grep -q localstack; then \
		echo "  ☁️  LocalStack S3: http://localhost:4566"; \
	fi

# Open RabbitMQ Management UI
rabbitmq-ui:
	@echo "$(GREEN)🐰 Opening RabbitMQ Management UI...$(NC)"
	@open http://localhost:15672 2>/dev/null || xdg-open http://localhost:15672 2>/dev/null || echo "Open http://localhost:15672 manually"

# Shell access
shell:
	@echo "$(GREEN)🐚 Opening shell in worker container...$(NC)"
	@docker-compose exec media-convert-worker /bin/bash

# Logs
logs:
	@docker-compose logs -f

logs-worker:
	@docker-compose logs -f media-convert-worker

# Environment Testing
test-env:
	@echo "$(GREEN)🧪 Testing environment connectivity...$(NC)"
	@if docker ps | grep -q media-convert-worker; then \
		docker exec media-convert-worker python /app/scripts/test_environment.py; \
	else \
		echo "$(YELLOW)⚠️ Worker container not running. Starting temporary container for tests...$(NC)"; \
		docker run --rm --network media-convert_media-convert-network \
			-e RABBITMQ_HOST=rabbitmq \
			-e RABBITMQ_USER=admin \
			-e RABBITMQ_PASSWORD=admin123 \
			-e RABBITMQ_VHOST=media_convert \
			media-convert-worker:latest python /app/scripts/test_environment.py; \
	fi

test-celery:
	@echo "$(GREEN)🧪 Testing Celery task execution...$(NC)"
	@docker-compose exec media-convert-worker python -c "from app.tasks.test_tasks import test_basic_task; result = test_basic_task.delay(); print(f'Task ID: {result.id}'); print(f'Result: {result.get(timeout=30)}')"

# Unit Testing
test:
	@echo "$(GREEN)🧪 Running unit tests...$(NC)"
	@docker-compose exec media-convert-worker pytest -v

test-build:
	@echo "$(GREEN)🧪 Running tests (building if needed)...$(NC)"
	@docker-compose run --rm media-convert-worker pytest -v

# Code quality
lint:
	@echo "$(GREEN)🔍 Running linting...$(NC)"
	@docker-compose exec media-convert-worker flake8 app/
	@docker-compose exec media-convert-worker mypy app/

format:
	@echo "$(GREEN)🎨 Formatting code...$(NC)"
	@docker-compose exec media-convert-worker black app/
	@docker-compose exec media-convert-worker isort app/

# Cleanup
down:
	@echo "$(GREEN)🛑 Stopping all services...$(NC)"
	@docker-compose --profile worker --profile s3-local down

clean:
	@echo "$(RED)🧹 Cleaning up containers and volumes...$(NC)"
	@docker-compose --profile worker --profile s3-local down -v
	@docker system prune -f

reset: clean setup
	@echo "$(GREEN)🔄 Complete reset done!$(NC)"

# Install dependencies locally (for IDE support)
install:
	@echo "$(GREEN)📦 Installing dependencies locally...$(NC)"
	@pip install -r requirements-dev.txt

# Build worker image
build:
	@echo "$(GREEN)🔨 Building worker image...$(NC)"
	@docker-compose build media-convert-worker

build-prod:
	@echo "$(GREEN)🔨 Building production image...$(NC)"
	@docker build -f docker/Dockerfile -t media-convert:latest .
