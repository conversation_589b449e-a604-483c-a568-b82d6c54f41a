# Media Convert Service - Makefile

.PHONY: help setup up down clean logs worker test lint format

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make setup    - Initial setup"
	@echo "  make up       - Start RabbitMQ"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make format   - Format code"
	@echo "  make logs     - View logs"
	@echo "  make clean    - Clean up"

# Setup
setup:
	@mkdir -p logs temp
	@if [ ! -f .env ]; then cp .env.example .env; fi

# Services
up:
	@docker-compose up -d rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down

# Testing
test:
	@docker-compose exec media-convert-worker pytest -v

# Code quality
lint:
	@docker-compose exec media-convert-worker ruff check app/ --fix

format:
	@docker-compose exec media-convert-worker ruff format app/

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Cleanup
clean:
	@docker-compose --profile worker down -v
	@docker system prune -f

# Build
build:
	@docker-compose build media-convert-worker
