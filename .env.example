# Media Convert Service Environment Variables

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
RABBITMQ_VHOST=media_convert

# Redis Configuration (for result backend)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# AWS Configuration
AWS_REGION=us-east-1
AWS_S3_BUCKET=media-convert-bucket
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
# For development with LocalStack
AWS_ENDPOINT_URL=http://localhost:4566

# Celery Configuration
CELERY_BROKER_URL=pyamqp://admin:admin123@localhost:5672/media_convert
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_CONCURRENCY=2

# FFmpeg Configuration
FFMPEG_PATH=/usr/bin/ffmpeg
FFMPEG_PROBE_PATH=/usr/bin/ffprobe

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Configuration
APP_NAME=media-convert
APP_VERSION=1.0.0
ENVIRONMENT=development

# Monitoring Configuration
PROMETHEUS_PORT=8000
HEALTH_CHECK_PORT=8080

# Temporary Files Configuration
TEMP_DIR=/tmp/media-convert
MAX_TEMP_FILE_AGE=3600

# Video Conversion Configuration
MAX_VIDEO_DURATION=7200  # 2 hours in seconds
SUPPORTED_INPUT_FORMATS=mp4,avi,mov,mkv
OUTPUT_FORMATS=dash

# Notification Configuration
NOTIFICATION_QUEUE=notifications
WEBHOOK_URL=https://your-webhook-url.com/notifications
