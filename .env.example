# Media Convert Service Environment Variables

# RabbitMQ Configuration (broker and result backend)
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
RABBITMQ_VHOST=media_convert

# AWS Configuration
AWS_REGION=us-east-1
AWS_S3_BUCKET=media-convert-bucket
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
# Optional: For development with LocalStack
# AWS_ENDPOINT_URL=http://localhost:4566

# Celery Configuration
CELERY_BROKER_URL=pyamqp://admin:admin123@localhost:5672/media_convert
CELERY_RESULT_BACKEND=rpc://
CELERY_CONCURRENCY=2

# FFmpeg Configuration
FFMPEG_PATH=/usr/bin/ffmpeg
FFMPEG_PROBE_PATH=/usr/bin/ffprobe

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Configuration
APP_NAME=media-convert
APP_VERSION=1.0.0
ENVIRONMENT=development

# Monitoring Configuration
PROMETHEUS_PORT=8000
HEALTH_CHECK_PORT=8080

# Temporary Files Configuration
TEMP_DIR=/tmp/media-convert
MAX_TEMP_FILE_AGE=3600

# Video Conversion Configuration
# Maximum video duration in seconds (7200 = 2 hours)
MAX_VIDEO_DURATION=7200
# Supported input formats (comma-separated)
SUPPORTED_INPUT_FORMATS=mp4,avi,mov,mkv
# Default bitrates optimized for LMS: 720p,480p,360p,240p (no upscaling)

# EKS/Container Configuration
# Maximum parallel upload workers (auto-detected if not set)
MAX_UPLOAD_WORKERS=6
# FFmpeg thread count (should match container CPU limit)
FFMPEG_THREADS=2
# FFmpeg preset for CPU/quality balance (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
FFMPEG_PRESET=medium
OUTPUT_FORMATS=dash

# Notification Configuration
NOTIFICATION_QUEUE=notifications
WEBHOOK_URL=https://your-webhook-url.com/notifications
