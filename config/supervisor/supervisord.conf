[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

[supervisord]
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=true
minfds=1024
minprocs=200
user=root

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

; Include Celery worker configuration
[program:celery-worker]
command=celery -A app.celery_app worker --loglevel=info --queues=video_conversion,notifications --concurrency=%(ENV_CELERY_CONCURRENCY)s --max-tasks-per-child=1000
directory=/app
user=celery
numprocs=1
autostart=true
autorestart=true
startsecs=10
startretries=3
stopsignal=TERM
stopwaitsecs=600
redirect_stderr=true
stdout_logfile=/var/log/celery/worker.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=10
environment=
    PYTHONPATH="/app",
    C_FORCE_ROOT="1",
    CELERY_OPTIMIZATION="fair"
killasgroup=true
stopasgroup=true
priority=999

; Health check program
[program:health-check]
command=python -m app.utils.health_check
directory=/app
user=celery
autostart=true
autorestart=true
startsecs=5
startretries=3
redirect_stderr=true
stdout_logfile=/var/log/health/health_check.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
priority=1000
