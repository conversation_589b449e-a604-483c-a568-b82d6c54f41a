"""
Celery Settings Configuration

This module contains detailed Celery configuration settings that can be
imported and used by the main Celery application.
"""

from decouple import config

# Broker settings
BROKER_URL = config('CELERY_BROKER_URL', default='pyamqp://guest@localhost//')
BROKER_CONNECTION_RETRY_ON_STARTUP = True
BROKER_CONNECTION_RETRY = True
BROKER_CONNECTION_MAX_RETRIES = 10

# Result backend settings
RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')
RESULT_BACKEND_TRANSPORT_OPTIONS = {
    'master_name': 'mymaster',
    'visibility_timeout': 3600,
}

# Task settings
TASK_SERIALIZER = 'json'
RESULT_SERIALIZER = 'json'
ACCEPT_CONTENT = ['json']
TIMEZONE = 'UTC'
ENABLE_UTC = True

# Worker settings
WORKER_PREFETCH_MULTIPLIER = 1
WORKER_MAX_TASKS_PER_CHILD = 1000
WORKER_DISABLE_RATE_LIMITS = True
WORKER_SEND_TASK_EVENTS = True
WORKER_HIJACK_ROOT_LOGGER = False

# Task execution settings
TASK_ACKS_LATE = True
TASK_REJECT_ON_WORKER_LOST = True
TASK_SEND_SENT_EVENT = True

# Retry settings
TASK_DEFAULT_RETRY_DELAY = 60
TASK_MAX_RETRIES = 3

# Queue settings
TASK_ROUTES = {
    'app.tasks.video_conversion.*': {'queue': 'video_conversion'},
    'app.tasks.notification.*': {'queue': 'notifications'},
}

# Monitoring settings
WORKER_SEND_TASK_EVENTS = True
TASK_SEND_SENT_EVENT = True

# Security settings
WORKER_HIJACK_ROOT_LOGGER = False
WORKER_LOG_COLOR = False

# Performance settings
CELERYD_TASK_TIME_LIMIT = 3600  # 1 hour for video conversion
CELERYD_TASK_SOFT_TIME_LIMIT = 3300  # 55 minutes soft limit
