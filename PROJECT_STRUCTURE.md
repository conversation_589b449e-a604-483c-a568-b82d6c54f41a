# Estrutura do Projeto Media Convert

```
media-convert/
├── app/                          # Código principal da aplicação
│   ├── __init__.py
│   ├── celery_app.py            # Configuração do Celery
│   ├── tasks/                   # Tarefas Celery
│   │   ├── __init__.py
│   │   ├── video_conversion.py  # Tarefa principal de conversão
│   │   └── notification.py      # Tarefas de notificação
│   ├── utils/                   # Utilitários
│   │   ├── __init__.py
│   │   ├── s3_client.py        # Cliente S3
│   │   ├── ffmpeg_wrapper.py   # Wrapper para FFmpeg
│   │   └── logger.py           # Configuração de logging
│   └── models/                  # Modelos de dados
│       ├── __init__.py
│       └── conversion_job.py    # Modelo para jobs de conversão
├── config/                      # Configurações
│   ├── __init__.py
│   ├── celery/                 # Configurações Celery
│   │   ├── __init__.py
│   │   └── settings.py
│   ├── supervisor/             # Configurações Supervisor
│   │   └── supervisord.conf
│   └── aws/                    # Configurações AWS
│       ├── __init__.py
│       └── s3_config.py
├── supervisor/                  # Arquivos específicos do Supervisor
│   ├── supervisord.conf        # Configuração principal
│   └── programs/               # Configurações de programas
│       └── celery-worker.conf
├── docker/                     # Arquivos Docker
│   ├── Dockerfile
│   ├── docker-compose.yml      # Para desenvolvimento local
│   └── entrypoint.sh
├── k8s/                        # Manifestos Kubernetes
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── hpa.yaml
│   ├── configmap.yaml
│   └── serviceaccount.yaml
├── scripts/                    # Scripts auxiliares
│   ├── deployment/
│   │   ├── build.sh           # Script de build
│   │   └── deploy.sh          # Script de deploy
│   └── monitoring/
│       └── health_check.py    # Health check
├── tests/                      # Testes
│   ├── __init__.py
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── logs/                       # Logs (montado como volume)
├── temp/                       # Arquivos temporários
├── requirements.txt            # Dependências Python
├── requirements-dev.txt        # Dependências de desenvolvimento
├── .env.example               # Exemplo de variáveis de ambiente
├── .gitignore
└── README.md
```

## Descrição dos Diretórios

### `/app` - Código Principal
- **celery_app.py**: Inicialização e configuração do Celery
- **tasks/**: Todas as tarefas Celery organizadas por funcionalidade
- **utils/**: Utilitários reutilizáveis (S3, FFmpeg, logging)
- **models/**: Modelos de dados para estruturar informações

### `/config` - Configurações
- **celery/**: Configurações específicas do Celery
- **supervisor/**: Configurações do Supervisor
- **aws/**: Configurações AWS (S3, IAM, etc.)

### `/supervisor` - Supervisor
- Configurações dedicadas para gerenciamento de processos
- Separado para facilitar customização e debugging

### `/docker` - Containerização
- Dockerfile otimizado para produção
- docker-compose.yml para desenvolvimento local
- Scripts de entrada personalizados

### `/k8s` - Kubernetes
- Manifestos organizados por tipo de recurso
- Configurações para EKS com IRSA

### `/scripts` - Automação
- Scripts de deployment e build
- Scripts de monitoramento e health check

### `/tests` - Testes
- Estrutura para testes unitários e de integração
- Fixtures para dados de teste
