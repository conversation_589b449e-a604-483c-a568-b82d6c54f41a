# Production Dockerfile for Media Convert Service
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    gosu \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create celery user
RUN useradd --create-home --shell /bin/bash --uid 1000 celery

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/log/celery /tmp/media-convert \
    && chown -R celery:celery /var/log/celery /tmp/media-convert

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip cache purge

# Copy application code
COPY app/ ./app/
COPY docker/entrypoint.sh /entrypoint.sh

# Set permissions
RUN chmod +x /entrypoint.sh \
    && chown -R celery:celery /app

# Health check using our health check module
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD python -m app.utils.health_check || exit 1

# Use our custom entrypoint
ENTRYPOINT ["/entrypoint.sh"]
