{"rabbit_version": "3.12.0", "users": [{"name": "admin", "password_hash": "gqM2ULfURUaxg8oLu0a0Lw==", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "media_convert"}], "permissions": [{"user": "admin", "vhost": "media_convert", "configure": ".*", "write": ".*", "read": ".*"}], "queues": [{"name": "video_conversion", "vhost": "media_convert", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 1000}}, {"name": "notifications", "vhost": "media_convert", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 5000}}, {"name": "test", "vhost": "media_convert", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "", "vhost": "media_convert", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": []}