# Development Dockerfile for Media Convert Service
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    gosu \
    && rm -rf /var/lib/apt/lists/*

# Create celery user
RUN useradd --create-home --shell /bin/bash celery

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/log/celery /tmp/media-convert \
    && chown -R celery:celery /var/log/celery /tmp/media-convert

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy and set permissions for entrypoint
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set permissions
RUN chown -R celery:celery /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD celery -A app.celery_app inspect ping || exit 1

# Use our custom entrypoint
ENTRYPOINT ["/entrypoint.sh"]
