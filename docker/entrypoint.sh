#!/bin/bash

# Media Convert Service - Docker Entrypoint
# This script starts the Celery worker with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Media Convert Service...${NC}"

# Environment variables with defaults
CELERY_APP=${CELERY_APP:-"app.celery_app"}
CELERY_LOGLEVEL=${CELERY_LOGLEVEL:-"info"}
CELERY_QUEUES=${CELERY_QUEUES:-"video_conversion,notifications,test"}
CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
CELERY_MAX_TASKS_PER_CHILD=${CELERY_MAX_TASKS_PER_CHILD:-1000}

# Wait for RabbitMQ to be ready
echo -e "${YELLOW}⏳ Waiting for RabbitMQ to be ready...${NC}"
while ! python -c "
import pika
import sys
from decouple import config

try:
    rabbitmq_host = config('RABBITMQ_HOST', default='localhost')
    rabbitmq_port = config('RABBITMQ_PORT', default=5672, cast=int)
    rabbitmq_user = config('RABBITMQ_USER', default='admin')
    rabbitmq_password = config('RABBITMQ_PASSWORD', default='admin123')
    rabbitmq_vhost = config('RABBITMQ_VHOST', default='media_convert')
    
    credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_password)
    parameters = pika.ConnectionParameters(
        host=rabbitmq_host,
        port=rabbitmq_port,
        virtual_host=rabbitmq_vhost,
        credentials=credentials,
        connection_attempts=1,
        retry_delay=1
    )
    
    connection = pika.BlockingConnection(parameters)
    connection.close()
    print('RabbitMQ is ready!')
except Exception as e:
    print(f'RabbitMQ not ready: {e}')
    sys.exit(1)
"; do
    echo -e "${YELLOW}⏳ RabbitMQ not ready, waiting 5 seconds...${NC}"
    sleep 5
done

echo -e "${GREEN}✅ RabbitMQ is ready!${NC}"

# Create necessary directories
mkdir -p /var/log/celery /tmp/media-convert

# Set proper permissions
chown -R celery:celery /var/log/celery /tmp/media-convert

# Health check function
health_check() {
    echo -e "${YELLOW}🏥 Running health check...${NC}"
    python -m app.utils.health_check
}

# Trap SIGTERM for graceful shutdown
trap 'echo -e "${YELLOW}📴 Received SIGTERM, shutting down gracefully...${NC}"; exit 0' SIGTERM

# Start Celery worker
echo -e "${GREEN}👷 Starting Celery worker...${NC}"
echo -e "${GREEN}📋 Configuration:${NC}"
echo -e "  App: ${CELERY_APP}"
echo -e "  Log Level: ${CELERY_LOGLEVEL}"
echo -e "  Queues: ${CELERY_QUEUES}"
echo -e "  Concurrency: ${CELERY_CONCURRENCY}"
echo -e "  Max Tasks Per Child: ${CELERY_MAX_TASKS_PER_CHILD}"
echo ""

# Execute Celery worker as celery user
exec gosu celery celery -A ${CELERY_APP} worker \
    --loglevel=${CELERY_LOGLEVEL} \
    --queues=${CELERY_QUEUES} \
    --concurrency=${CELERY_CONCURRENCY} \
    --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD} \
    --without-gossip \
    --without-mingle \
    --without-heartbeat
