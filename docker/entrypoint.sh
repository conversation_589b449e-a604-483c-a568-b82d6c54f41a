#!/bin/bash

# Media Convert Service - Docker Entrypoint
# This script starts the Celery worker with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Media Convert Service...${NC}"

# Environment variables with defaults
CELERY_APP=${CELERY_APP:-"app.celery_app"}
CELERY_LOGLEVEL=${CELERY_LOGLEVEL:-"info"}
CELERY_QUEUES=${CELERY_QUEUES:-"video_conversion,notifications,test"}
CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
CELERY_MAX_TASKS_PER_CHILD=${CELERY_MAX_TASKS_PER_CHILD:-1000}

# Note: We let Celery handle RabbitMQ connection retries
# Docker compose depends_on and health checks ensure RabbitMQ is ready



# Create necessary directories and set permissions
echo -e "${YELLOW}📁 Setting up directories...${NC}"
mkdir -p /var/log/celery /tmp/media-convert
chown -R celery:celery /var/log/celery /tmp/media-convert

# Trap SIGTERM for graceful shutdown
trap 'echo -e "${YELLOW}📴 Received SIGTERM, shutting down gracefully...${NC}"; exit 0' SIGTERM

# Start Celery worker
echo -e "${GREEN}👷 Starting Celery worker...${NC}"
echo -e "${GREEN}📋 Configuration:${NC}"
echo -e "  App: ${CELERY_APP}"
echo -e "  Log Level: ${CELERY_LOGLEVEL}"
echo -e "  Queues: ${CELERY_QUEUES}"
echo -e "  Concurrency: ${CELERY_CONCURRENCY}"
echo -e "  Max Tasks Per Child: ${CELERY_MAX_TASKS_PER_CHILD}"
echo ""

# Execute Celery worker as celery user
exec gosu celery celery -A ${CELERY_APP} worker \
    --loglevel=${CELERY_LOGLEVEL} \
    --queues=${CELERY_QUEUES} \
    --concurrency=${CELERY_CONCURRENCY} \
    --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD} \
    --without-gossip \
    --without-mingle \
    --without-heartbeat
