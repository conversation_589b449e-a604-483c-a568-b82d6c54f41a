#!/usr/bin/env python3
"""Database management commands for Media Convert Service"""

import sys
import argparse
from app.database.connection import test_connection, engine
from app.database.models import VideoConversion
from app.utils.logger import get_logger

logger = get_logger(__name__)


def test_db():
    """Test database connection"""
    logger.info("🔍 Testing database connection...")
    if test_connection():
        logger.info("✅ Database connection successful!")
        return True
    else:
        logger.error("❌ Database connection failed!")
        return False


def create_tables():
    """Create all tables (for development only)"""
    logger.info("🔨 Creating database tables...")
    try:
        from app.database.connection import Base
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Tables created successfully!")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create tables: {e}")
        return False


def drop_tables():
    """Drop all tables (DANGEROUS - for development only)"""
    logger.warning("⚠️ DROPPING ALL TABLES - This will delete all data!")
    confirm = input("Type 'YES' to confirm: ")
    if confirm != "YES":
        logger.info("❌ Operation cancelled")
        return False
    
    try:
        from app.database.connection import Base
        Base.metadata.drop_all(bind=engine)
        logger.info("✅ Tables dropped successfully!")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to drop tables: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Database management commands")
    parser.add_argument("command", choices=["test", "create", "drop"], 
                       help="Command to execute")
    
    args = parser.parse_args()
    
    if args.command == "test":
        success = test_db()
    elif args.command == "create":
        success = create_tables()
    elif args.command == "drop":
        success = drop_tables()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
