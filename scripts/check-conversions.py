#!/usr/bin/env python3
"""Check video conversion status and list conversions"""

import sys
import argparse
from app.services.video_conversion_service import VideoConversionService
from app.api.conversion_api import get_conversion_status
from app.utils.logger import get_logger

logger = get_logger(__name__)


def check_status(job_id: str):
    """Check status of a specific conversion"""
    logger.info(f"🔍 Checking status for job: {job_id}")
    
    result = get_conversion_status(job_id)
    
    if result["success"]:
        conversion = result["conversion"]
        print(f"\n📊 Conversion Status for Job: {job_id}")
        print(f"ID: {conversion['id']}")
        print(f"Status: {conversion['status']}")
        print(f"Input: {conversion['input_path']}")
        print(f"Output: {conversion['output_path'] or 'N/A'}")
        print(f"Created: {conversion['created_at']}")
        print(f"Updated: {conversion['updated_at']}")
        print(f"Duration: {conversion['duration_seconds'] or 'N/A'} seconds")
        
        if conversion['error_message']:
            print(f"Error: {conversion['error_message']}")
        
        if conversion['metadata_json']:
            print(f"Metadata: {conversion['metadata_json']}")
    else:
        print(f"❌ {result['error']}")


def list_conversions(status: str = None, limit: int = 10):
    """List recent conversions"""
    logger.info(f"📋 Listing conversions (status: {status or 'all'}, limit: {limit})")
    
    conversions = VideoConversionService.list_conversions(
        status=status,
        limit=limit
    )
    
    if not conversions:
        print("📭 No conversions found")
        return
    
    print(f"\n📋 Recent Conversions ({len(conversions)} found):")
    print("-" * 80)
    
    for conv in conversions:
        status_emoji = {
            "PENDING": "⏳",
            "PROCESSING": "🔄", 
            "COMPLETED": "✅",
            "FAILED": "❌"
        }.get(conv.status, "❓")
        
        duration = f"{conv.duration_seconds}s" if conv.duration_seconds else "N/A"
        
        print(f"{status_emoji} {conv.job_id[:8]}... | {conv.status:10} | {duration:6} | {conv.created_at}")
        print(f"   Input:  {conv.input_path}")
        if conv.output_path:
            print(f"   Output: {conv.output_path}")
        if conv.error_message:
            print(f"   Error:  {conv.error_message[:60]}...")
        print()


def main():
    parser = argparse.ArgumentParser(description="Check video conversion status")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Check status of specific conversion")
    status_parser.add_argument("job_id", help="Job ID to check")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List recent conversions")
    list_parser.add_argument("--status", choices=["PENDING", "PROCESSING", "COMPLETED", "FAILED"],
                           help="Filter by status")
    list_parser.add_argument("--limit", type=int, default=10, help="Number of records to show")
    
    args = parser.parse_args()
    
    if args.command == "status":
        check_status(args.job_id)
    elif args.command == "list":
        list_conversions(args.status, args.limit)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
