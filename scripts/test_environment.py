#!/usr/bin/env python3
"""
Environment Test Script for Media Convert Service

This script tests the basic functionality of the development environment:
- RabbitMQ connectivity
- Redis connectivity  
- Celery task execution
- Queue routing
"""

import sys
import time
import redis
from celery import Celery
from decouple import config

# Colors for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_test(message):
    print(f"{Colors.BLUE}🧪 {message}{Colors.ENDC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.ENDC}")

def test_redis_connection():
    """Test Redis connectivity."""
    print_test("Testing Redis connection...")
    
    try:
        redis_host = config('REDIS_HOST', default='localhost')
        redis_port = config('REDIS_PORT', default=6379, cast=int)
        redis_db = config('REDIS_DB', default=0, cast=int)
        
        r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
        
        # Test basic operations
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        r.delete('test_key')
        
        if value == b'test_value':
            print_success(f"Redis connection successful ({redis_host}:{redis_port})")
            return True
        else:
            print_error("Redis test failed - unexpected value")
            return False
            
    except Exception as e:
        print_error(f"Redis connection failed: {e}")
        return False

def test_redis_broker():
    """Test Redis as Celery broker."""
    print_test("Testing Redis as Celery broker...")

    try:
        redis_host = config('REDIS_HOST', default='localhost')
        redis_port = config('REDIS_PORT', default=6379, cast=int)
        redis_db = config('REDIS_DB', default=0, cast=int)

        r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)

        # Test broker functionality by checking if we can push/pop from a list
        test_key = 'celery_test_queue'
        r.lpush(test_key, 'test_message')
        message = r.rpop(test_key)

        if message == b'test_message':
            print_success(f"Redis broker test successful ({redis_host}:{redis_port}/{redis_db})")
            return True
        else:
            print_error("Redis broker test failed - unexpected message")
            return False

    except Exception as e:
        print_error(f"Redis broker test failed: {e}")
        return False

def test_celery_connection():
    """Test Celery connectivity."""
    print_test("Testing Celery connection...")
    
    try:
        # Import Celery app
        from app.celery_app import celery_app
        
        # Test Celery inspect
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print_success("Celery connection successful - workers detected")
            for worker, worker_stats in stats.items():
                print(f"  📊 Worker: {worker}")
            return True
        else:
            print_warning("Celery connection successful but no workers detected")
            print("  💡 This is normal if workers aren't running yet")
            return True
            
    except Exception as e:
        print_error(f"Celery connection failed: {e}")
        return False

def test_celery_task_execution():
    """Test Celery task execution (requires running worker)."""
    print_test("Testing Celery task execution...")
    
    try:
        from app.tasks.test_tasks import test_basic_task
        
        # Send task
        result = test_basic_task.delay()
        print(f"  📤 Task sent with ID: {result.id}")
        
        # Wait for result with timeout
        try:
            task_result = result.get(timeout=30)
            print_success("Celery task execution successful")
            print(f"  📋 Result: {task_result}")
            return True
        except Exception as e:
            print_warning(f"Task execution timeout or failed: {e}")
            print("  💡 Make sure a Celery worker is running")
            return False
            
    except Exception as e:
        print_error(f"Celery task test failed: {e}")
        return False

def main():
    """Run all tests."""
    print(f"{Colors.BOLD}🚀 Media Convert Environment Tests{Colors.ENDC}")
    print("=" * 50)
    
    tests = [
        ("Redis Connection", test_redis_connection),
        ("Redis Broker", test_redis_broker),
        ("Celery Connection", test_celery_connection),
        ("Celery Task Execution", test_celery_task_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{Colors.BOLD}Testing {test_name}:{Colors.ENDC}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n{Colors.BOLD}📊 Test Summary:{Colors.ENDC}")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print_success("🎉 All tests passed! Environment is ready.")
        return 0
    else:
        print_error("❌ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
