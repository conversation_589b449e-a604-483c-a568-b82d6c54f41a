#!/usr/bin/env python3
"""
Environment Test Script for Media Convert Service

This script tests the basic functionality of the development environment:
- RabbitMQ connectivity
- Redis connectivity  
- Celery task execution
- Queue routing
"""

import sys
import time
import os
from decouple import config

# Add the app directory to Python path if running outside container
if '/app' not in sys.path:
    app_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, app_path)

# Colors for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_test(message):
    print(f"{Colors.BLUE}🧪 {message}{Colors.ENDC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.ENDC}")

def test_celery_broker():
    """Test Celery broker connectivity."""
    print_test("Testing Celery broker connection...")

    try:
        try:
            from app.celery_app import celery_app
        except ImportError:
            print_error("Cannot import app.celery_app - make sure you're running inside the container or from the project root")
            return False

        # Test broker connection using Celery
        with celery_app.connection() as connection:
            connection.ensure_connection(max_retries=3)

        print_success("Celery broker connection successful")
        return True

    except Exception as e:
        print_error(f"Celery broker connection failed: {e}")
        return False

def test_celery_workers():
    """Test Celery workers."""
    print_test("Testing Celery workers...")

    try:
        try:
            from app.celery_app import celery_app
        except ImportError:
            print_error("Cannot import app.celery_app - make sure you're running inside the container or from the project root")
            return False

        # Test if we can inspect workers
        inspect = celery_app.control.inspect()
        stats = inspect.stats()

        if stats:
            print_success(f"Celery workers found: {list(stats.keys())}")
            for worker, worker_stats in stats.items():
                print(f"  📊 Worker: {worker}")
        else:
            print_success("Celery broker is healthy (no workers detected yet)")
            print("  💡 This is normal if workers aren't running")

        return True

    except Exception as e:
        print_error(f"Celery workers test failed: {e}")
        return False

def test_celery_connection():
    """Test Celery connectivity."""
    print_test("Testing Celery connection...")

    try:
        # Import Celery app
        try:
            from app.celery_app import celery_app
        except ImportError:
            print_error("Cannot import app.celery_app - make sure you're running inside the container or from the project root")
            return False
        
        # Test Celery inspect
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print_success("Celery connection successful - workers detected")
            for worker, worker_stats in stats.items():
                print(f"  📊 Worker: {worker}")
            return True
        else:
            print_warning("Celery connection successful but no workers detected")
            print("  💡 This is normal if workers aren't running yet")
            return True
            
    except Exception as e:
        print_error(f"Celery connection failed: {e}")
        return False

def test_celery_task_execution():
    """Test Celery task execution (requires running worker)."""
    print_test("Testing Celery task execution...")

    try:
        try:
            from app.tasks.test_tasks import test_basic_task
        except ImportError:
            print_error("Cannot import app.tasks.test_tasks - make sure you're running inside the container or from the project root")
            return False
        
        # Send task
        result = test_basic_task.delay()
        print(f"  📤 Task sent with ID: {result.id}")
        
        # Wait for result with timeout
        try:
            task_result = result.get(timeout=30)
            print_success("Celery task execution successful")
            print(f"  📋 Result: {task_result}")
            return True
        except Exception as e:
            print_warning(f"Task execution timeout or failed: {e}")
            print("  💡 Make sure a Celery worker is running")
            return False
            
    except Exception as e:
        print_error(f"Celery task test failed: {e}")
        return False

def main():
    """Run all tests."""
    print(f"{Colors.BOLD}🚀 Media Convert Environment Tests{Colors.ENDC}")
    print("=" * 50)
    
    tests = [
        ("Celery Broker", test_celery_broker),
        ("Celery Workers", test_celery_workers),
        ("Celery Connection", test_celery_connection),
        ("Celery Task Execution", test_celery_task_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{Colors.BOLD}Testing {test_name}:{Colors.ENDC}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n{Colors.BOLD}📊 Test Summary:{Colors.ENDC}")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print_success("🎉 All tests passed! Environment is ready.")
        return 0
    else:
        print_error("❌ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
