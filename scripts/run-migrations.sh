#!/bin/bash
set -e

echo "🔄 Running database migrations..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
python -c "
import time
import sys
from app.database.connection import test_connection

max_attempts = 30
attempt = 0

while attempt < max_attempts:
    if test_connection():
        print('✅ Database is ready!')
        sys.exit(0)
    attempt += 1
    print(f'⏳ Attempt {attempt}/{max_attempts} - Database not ready, waiting...')
    time.sleep(2)

print('❌ Database connection timeout')
sys.exit(1)
"

# Run migrations
echo "🔄 Applying database migrations..."
alembic upgrade head

echo "✅ Migrations completed successfully!"
