#!/usr/bin/env python3
"""
Teste de conversão real - Simula o fluxo completo de conversão de vídeo
"""

import os
import sys
import time
import json
from app.celery_app import celery_app
from app.tasks.video_conversion import convert_video_to_dash
from app.utils.s3_client import S3Client

def test_s3_connection():
    """Testa conexão com S3"""
    print("🔗 Testando conexão com S3...")
    
    try:
        s3_client = S3Client()
        
        # Tenta listar objetos do bucket (apenas para testar conexão)
        import boto3
        from botocore.exceptions import ClientError
        
        try:
            response = s3_client.s3_client.head_bucket(Bucket='keeps.kontent.media.hml')
            print("✅ Conexão S3 estabelecida com sucesso")
            print(f"   Bucket: keeps.kontent.media.hml")
            print(f"   Região: {s3_client.aws_region}")
            return True
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                print("❌ Bucket não encontrado")
            elif error_code == '403':
                print("❌ Sem permissão para acessar o bucket")
            else:
                print(f"❌ Erro S3: {error_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro de conexão S3: {e}")
        return False

def test_ffmpeg_availability():
    """Testa se FFmpeg está disponível"""
    print("🎥 Testando FFmpeg...")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg disponível: {version_line}")
            return True
        else:
            print("❌ FFmpeg não funcionou corretamente")
            return False
            
    except FileNotFoundError:
        print("❌ FFmpeg não encontrado")
        return False
    except Exception as e:
        print(f"❌ Erro ao testar FFmpeg: {e}")
        return False

def simulate_video_conversion():
    """Simula uma conversão de vídeo real"""
    print("🎬 Simulando conversão de vídeo...")
    
    # URLs de exemplo (você pode substituir por URLs reais do seu bucket)
    input_s3_url = "s3://keeps.kontent.media.hml/test-input/sample-video.mp4"
    output_s3_prefix = "s3://keeps.kontent.media.hml/test-output/"
    webhook_url = "https://webhook.site/unique-id"  # Substitua por um webhook real
    
    print(f"📥 Input: {input_s3_url}")
    print(f"📤 Output: {output_s3_prefix}")
    print(f"🔔 Webhook: {webhook_url}")
    
    try:
        # Envia task de conversão
        print("\n⏳ Enviando task de conversão...")
        result = convert_video_to_dash.delay(
            input_s3_url=input_s3_url,
            output_s3_prefix=output_s3_prefix,
            webhook_url=webhook_url,
            bitrates=None  # Usar bitrates padrão
        )
        
        print(f"✅ Task enviada com ID: {result.id}")
        print("⏳ Aguardando resultado (timeout: 5 minutos)...")
        
        # Aguarda resultado com timeout
        task_result = result.get(timeout=300)  # 5 minutos
        
        print("\n🎉 CONVERSÃO CONCLUÍDA!")
        print("📊 Resultado:")
        print(json.dumps(task_result, indent=2, default=str))
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erro na conversão: {e}")
        print("💡 Isso pode ser normal se o arquivo de entrada não existir")
        return False

def test_task_monitoring():
    """Testa monitoramento de tasks"""
    print("📊 Testando monitoramento de tasks...")
    
    try:
        inspect = celery_app.control.inspect()
        
        # Tasks ativas
        active = inspect.active()
        if active:
            print("🔄 Tasks ativas:")
            for worker, tasks in active.items():
                print(f"   Worker {worker}: {len(tasks)} tasks")
        else:
            print("✅ Nenhuma task ativa no momento")
        
        # Tasks agendadas
        scheduled = inspect.scheduled()
        if scheduled:
            print("📅 Tasks agendadas:")
            for worker, tasks in scheduled.items():
                print(f"   Worker {worker}: {len(tasks)} tasks")
        else:
            print("✅ Nenhuma task agendada")
        
        # Estatísticas
        stats = inspect.stats()
        if stats:
            print("📈 Estatísticas dos workers:")
            for worker, stat in stats.items():
                print(f"   {worker}:")
                print(f"     - Pool: {stat.get('pool', {}).get('max-concurrency', 'N/A')} workers")
                print(f"     - Total tasks: {stat.get('total', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no monitoramento: {e}")
        return False

def main():
    """Executa teste completo de conversão real"""
    print("🚀 TESTE DE CONVERSÃO REAL - MEDIA CONVERT")
    print("=" * 60)
    print("Este teste simula o funcionamento completo do sistema")
    print("=" * 60)
    
    tests = [
        ("Conexão S3", test_s3_connection),
        ("FFmpeg Disponível", test_ffmpeg_availability),
        ("Monitoramento Tasks", test_task_monitoring),
    ]
    
    # Executa testes preliminares
    print("\n📋 TESTES PRELIMINARES:")
    all_preliminary_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        success = test_func()
        if not success:
            all_preliminary_passed = False
    
    print("\n" + "=" * 60)
    
    if not all_preliminary_passed:
        print("⚠️  ALGUNS TESTES PRELIMINARES FALHARAM")
        print("💡 Verifique a configuração antes de prosseguir")
        return 1
    
    print("✅ TODOS OS TESTES PRELIMINARES PASSARAM!")
    
    # Pergunta se deve prosseguir com conversão real
    print("\n🎬 TESTE DE CONVERSÃO REAL:")
    print("⚠️  ATENÇÃO: Isso tentará converter um vídeo real!")
    print("💰 Pode gerar custos AWS se o arquivo existir")
    
    response = input("\n❓ Deseja prosseguir com o teste de conversão? (s/N): ")
    
    if response.lower() in ['s', 'sim', 'y', 'yes']:
        print("\n🎬 Iniciando teste de conversão...")
        success = simulate_video_conversion()
        
        if success:
            print("\n🎉 TESTE COMPLETO REALIZADO COM SUCESSO!")
            return 0
        else:
            print("\n⚠️  TESTE DE CONVERSÃO FALHOU (pode ser normal)")
            print("💡 Verifique se o arquivo de entrada existe no S3")
            return 1
    else:
        print("\n✅ TESTES PRELIMINARES CONCLUÍDOS")
        print("💡 Sistema pronto para conversões reais!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
