"""
Celery Application Configuration for Media Convert Service

This module initializes and configures the Celery application for video conversion tasks.
It uses RabbitMQ as both broker and result backend for maximum simplicity.
"""

import os
from celery import Celery
from decouple import config
from app.utils.logger import get_logger, log_task_start, log_task_success, log_task_failure

# Get configured logger
logger = get_logger(__name__)

# Environment variables with defaults
RABBITMQ_HOST = config('RABBITMQ_HOST', default='localhost')
RABBITMQ_PORT = config('RABBITMQ_PORT', default=5672, cast=int)
RABBITMQ_USER = config('RABBITMQ_USER', default='admin')
RABBITMQ_PASSWORD = config('RABBITMQ_PASSWORD', default='admin123')
RABBITMQ_VHOST = config('RABBITMQ_VHOST', default='media_convert')

# Celery configuration - <PERSON><PERSON><PERSON> as both broker and result backend
CELERY_BROKER_URL = f'pyamqp://{RABBITMQ_USER}:{RABBITMQ_PASSWORD}@{RABBITMQ_HOST}:{RABBITMQ_PORT}/{RABBITMQ_VHOST}'
CELERY_RESULT_BACKEND = 'rpc://'  # Use RabbitMQ as result backend

# Initialize Celery app
celery_app = Celery(
    'media_convert',
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=[
        'app.tasks.video_conversion',
        'app.tasks.notification',
        'app.tasks.test_tasks'
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,

    # Worker settings
    worker_prefetch_multiplier=1,  # Important for long-running tasks
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=True,

    # Task execution settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,

    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,

    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,

    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,

    # Security
    worker_hijack_root_logger=False,
    worker_log_color=False,

    # RabbitMQ specific settings
    broker_connection_retry_on_startup=True,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
)

# Queue definitions for RabbitMQ
celery_app.conf.task_routes = {
    'app.tasks.video_conversion.*': {'queue': 'video_conversion'},
    'app.tasks.notification.*': {'queue': 'notifications'},
    'app.tasks.test_tasks.*': {'queue': 'test'},
}

# Custom task base class for enhanced logging
class LoggingTask(celery_app.Task):
    """Base task class with enhanced logging capabilities."""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Log successful task completion."""
        log_task_success(
            task_name=self.name,
            task_id=task_id,
            result=retval
        )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Log task failure."""
        log_task_failure(
            task_name=self.name,
            task_id=task_id,
            error=str(exc),
            traceback=str(einfo)
        )

    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Log task retry."""
        logger.warning(
            "Task retry",
            extra={
                "task_id": task_id,
                "task_name": self.name,
                "exception": str(exc),
                "retry_count": self.request.retries,
                "event_type": "task_retry"
            }
        )

# Set the custom task base class
celery_app.Task = LoggingTask

# Health check task
@celery_app.task(bind=True)
def health_check(self):
    """Simple health check task for monitoring."""
    return {
        'status': 'healthy',
        'worker_id': self.request.id,
        'timestamp': self.request.eta or 'now'
    }

if __name__ == '__main__':
    celery_app.start()
