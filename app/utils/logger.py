"""
Simple logging configuration for Media Convert Service
"""

import logging
import sys
from decouple import config

# Simple configuration
LOG_LEVEL = config("LOG_LEVEL", default="INFO")

def get_logger(name: str = None):
    """Get a simple logger instance."""
    logger_name = name or __name__
    logger = logging.getLogger(logger_name)
    
    if not logger.handlers:
        # Simple console handler
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, LOG_LEVEL.upper()))
    
    return logger

# Default logger
logger = get_logger()
