"""
Simple logging configuration for Media Convert Service using Loguru
"""

import sys
from loguru import logger
from decouple import config

# Simple configuration
LOG_LEVEL = config("LOG_LEVEL", default="INFO")

# Remove default handler and add simple console handler
logger.remove()
logger.add(
    sys.stdout,
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
    level=LOG_LEVEL,
    colorize=True,
)


def get_logger(name: str = None):
    """Get a configured logger instance."""
    if name:
        return logger.bind(logger_name=name)
    return logger


def log_video_conversion_metrics(
    task_id: str,
    input_file_size: int,
    output_file_size: int,
    duration: float,
    bitrates: list,
    resolution: str,
):
    """Log video conversion metrics for monitoring."""
    logger.info(
        "Video conversion metrics",
        extra={
            "task_id": task_id,
            "input_file_size_bytes": input_file_size,
            "output_file_size_bytes": output_file_size,
            "conversion_duration_seconds": duration,
            "bitrates": bitrates,
            "resolution": resolution,
            "compression_ratio": output_file_size / input_file_size
            if input_file_size > 0
            else 0,
            "event_type": "video_metrics",
        },
    )
