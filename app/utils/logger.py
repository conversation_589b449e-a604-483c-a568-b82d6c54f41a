"""
Logging Configuration for Media Convert Service

This module configures loguru for structured logging compatible with ELK stack.
It provides different log handlers for development and production environments.
"""

import sys
import os
from loguru import logger
from decouple import config

# Environment configuration
LOG_LEVEL = config("LOG_LEVEL", default="INFO")
LOG_FORMAT = config("LOG_FORMAT", default="json")
ENVIRONMENT = config("ENVIRONMENT", default="development")


def configure_logging():
    """Configure loguru for structured logging compatible with ELK stack."""

    # Remove default handler
    logger.remove()

    # JSON format for ELK stack (production)
    json_format = (
        "{"
        '"timestamp": "{time:YYYY-MM-DD HH:mm:ss.SSS}", '
        '"level": "{level}", '
        '"logger": "{name}", '
        '"function": "{function}", '
        '"line": {line}, '
        '"message": "{message}", '
        '"extra": {extra}'
        "}"
    )

    # Human-readable format for development
    dev_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

    if ENVIRONMENT == "production":
        # Production logging - JSON format for ELK
        logger.add(
            "/var/log/celery/application.log",
            format=json_format,
            level=LOG_LEVEL,
            rotation="100 MB",
            retention="30 days",
            compression="gz",
            serialize=False,
            backtrace=True,
            diagnose=True,
        )

        # Console output in JSON for container logs
        logger.add(
            sys.stdout,
            format=json_format,
            level=LOG_LEVEL,
            serialize=False,
        )

    else:
        # Development logging - human readable
        logger.add(
            sys.stdout,
            format=dev_format,
            level=LOG_LEVEL,
            colorize=True,
        )

        # Simple file logging for development
        logger.add(
            "/var/log/celery/development.log",
            format=dev_format,
            level=LOG_LEVEL,
            rotation="10 MB",
            retention="3 days",
        )


def get_logger(name: str = None):
    """Get a configured logger instance."""
    if name:
        return logger.bind(logger_name=name)
    return logger


def log_task_start(
    task_name: str, task_id: str, args: tuple = None, kwargs: dict = None
):
    """Log task start with structured data."""
    logger.info(
        "Task started",
        extra={
            "task_name": task_name,
            "task_id": task_id,
            "args": args,
            "kwargs": kwargs,
            "event_type": "task_start",
        },
    )


def log_task_success(
    task_name: str, task_id: str, result: any = None, duration: float = None
):
    """Log task success with structured data."""
    logger.info(
        "Task completed successfully",
        extra={
            "task_name": task_name,
            "task_id": task_id,
            "result": result,
            "duration_seconds": duration,
            "event_type": "task_success",
        },
    )


def log_task_failure(task_name: str, task_id: str, error: str, traceback: str = None):
    """Log task failure with structured data."""
    logger.error(
        "Task failed",
        extra={
            "task_name": task_name,
            "task_id": task_id,
            "error": error,
            "traceback": traceback,
            "event_type": "task_failure",
        },
    )


def log_video_conversion_metrics(
    task_id: str,
    input_file_size: int,
    output_file_size: int,
    duration: float,
    bitrates: list,
    resolution: str,
):
    """Log video conversion metrics for monitoring."""
    logger.info(
        "Video conversion metrics",
        extra={
            "task_id": task_id,
            "input_file_size_bytes": input_file_size,
            "output_file_size_bytes": output_file_size,
            "conversion_duration_seconds": duration,
            "bitrates": bitrates,
            "resolution": resolution,
            "compression_ratio": output_file_size / input_file_size
            if input_file_size > 0
            else 0,
            "event_type": "video_metrics",
        },
    )


# Configure logging on module import
configure_logging()
