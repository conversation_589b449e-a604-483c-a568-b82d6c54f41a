"""
Health Check Module for Media Convert Service

This module provides health check functionality for the Celery worker
and can be used by Docker health checks, Kubernetes probes or monitoring systems.
"""

import sys
import time
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class HealthChecker:
    """Health checker for Media Convert Service components."""
    
    def __init__(self):
        pass
    
    def check_celery_broker(self):
        """Check Celery broker connectivity via Celery itself."""
        try:
            from app.celery_app import celery_app

            # Use Celery's own connection to check broker
            with celery_app.connection() as connection:
                connection.ensure_connection(max_retries=3)

            return True, "Celery broker is healthy"
        except Exception as e:
            return False, f"Celery broker check failed: {e}"
    
    def check_celery_workers(self):
        """Check if Celery workers are responding."""
        try:
            from app.celery_app import celery_app

            # Check if we can inspect workers
            inspect = celery_app.control.inspect()
            stats = inspect.stats()

            if stats:
                return True, f"Celery workers are healthy: {list(stats.keys())}"
            else:
                # No workers found, but connection is working
                return True, "Celery broker is healthy (no workers detected)"

        except Exception as e:
            return False, f"Celery workers check failed: {e}"
    
    def check_celery(self):
        """Check Celery worker status."""
        try:
            from app.celery_app import celery_app
            
            # Check if we can create a Celery instance
            inspect = celery_app.control.inspect()
            
            # Try to get worker stats (this will work even if no workers are running)
            stats = inspect.stats()
            
            return True, "Celery is healthy"
        except Exception as e:
            return False, f"Celery check failed: {e}"
    
    def check_all(self):
        """Run all health checks."""
        checks = {
            'celery_broker': self.check_celery_broker,
            'celery_workers': self.check_celery_workers,
            'celery_app': self.check_celery
        }
        
        results = {}
        all_healthy = True
        
        for name, check_func in checks.items():
            try:
                healthy, message = check_func()
                results[name] = {
                    'healthy': healthy,
                    'message': message
                }
                if not healthy:
                    all_healthy = False
            except Exception as e:
                results[name] = {
                    'healthy': False,
                    'message': f"Health check error: {e}"
                }
                all_healthy = False
        
        return all_healthy, results

def main():
    """Main health check function for command line usage."""
    checker = HealthChecker()
    
    print("🏥 Media Convert Service Health Check")
    print("=" * 40)
    
    all_healthy, results = checker.check_all()
    
    for service, result in results.items():
        status = "✅ HEALTHY" if result['healthy'] else "❌ UNHEALTHY"
        print(f"{service.upper()}: {status}")
        print(f"  Message: {result['message']}")
        print()
    
    if all_healthy:
        print("🎉 All services are healthy!")
        logger.info("Health check passed", extra={"results": results})
        return 0
    else:
        print("❌ Some services are unhealthy!")
        logger.error("Health check failed", extra={"results": results})
        return 1

if __name__ == "__main__":
    sys.exit(main())
