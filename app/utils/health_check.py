"""
Health Check Module for Media Convert Service

This module provides health check functionality for the Celery worker
and can be used by Kubernetes probes or monitoring systems.
"""

import sys
import time
import pika
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class HealthChecker:
    """Health checker for Media Convert Service components."""
    
    def __init__(self):
        self.rabbitmq_host = config('RABBITMQ_HOST', default='localhost')
        self.rabbitmq_port = config('RABBITMQ_PORT', default=5672, cast=int)
        self.rabbitmq_user = config('RABBITMQ_USER', default='admin')
        self.rabbitmq_password = config('RABBITMQ_PASSWORD', default='admin123')
        self.rabbitmq_vhost = config('RABBITMQ_VHOST', default='media_convert')
    
    def check_rabbitmq(self):
        """Check RabbitMQ connectivity."""
        try:
            credentials = pika.PlainCredentials(self.rabbitmq_user, self.rabbitmq_password)
            parameters = pika.ConnectionParameters(
                host=self.rabbitmq_host,
                port=self.rabbitmq_port,
                virtual_host=self.rabbitmq_vhost,
                credentials=credentials,
                connection_attempts=3,
                retry_delay=1
            )

            connection = pika.BlockingConnection(parameters)
            connection.close()
            return True, "RabbitMQ is healthy"
        except Exception as e:
            return False, f"RabbitMQ check failed: {e}"
    
    def check_rabbitmq_queues(self):
        """Check RabbitMQ queues."""
        try:
            credentials = pika.PlainCredentials(self.rabbitmq_user, self.rabbitmq_password)
            parameters = pika.ConnectionParameters(
                host=self.rabbitmq_host,
                port=self.rabbitmq_port,
                virtual_host=self.rabbitmq_vhost,
                credentials=credentials,
                connection_attempts=3,
                retry_delay=1
            )

            connection = pika.BlockingConnection(parameters)
            channel = connection.channel()

            # Check if main queues exist
            queues = ['video_conversion', 'notifications', 'test']
            for queue in queues:
                channel.queue_declare(queue=queue, passive=True)

            connection.close()
            return True, "RabbitMQ queues are healthy"
        except Exception as e:
            return False, f"RabbitMQ queues check failed: {e}"
    
    def check_celery(self):
        """Check Celery worker status."""
        try:
            from app.celery_app import celery_app
            
            # Check if we can create a Celery instance
            inspect = celery_app.control.inspect()
            
            # Try to get worker stats (this will work even if no workers are running)
            stats = inspect.stats()
            
            return True, "Celery is healthy"
        except Exception as e:
            return False, f"Celery check failed: {e}"
    
    def check_all(self):
        """Run all health checks."""
        checks = {
            'rabbitmq': self.check_rabbitmq,
            'rabbitmq_queues': self.check_rabbitmq_queues,
            'celery': self.check_celery
        }
        
        results = {}
        all_healthy = True
        
        for name, check_func in checks.items():
            try:
                healthy, message = check_func()
                results[name] = {
                    'healthy': healthy,
                    'message': message
                }
                if not healthy:
                    all_healthy = False
            except Exception as e:
                results[name] = {
                    'healthy': False,
                    'message': f"Health check error: {e}"
                }
                all_healthy = False
        
        return all_healthy, results

def main():
    """Main health check function for command line usage."""
    checker = HealthChecker()
    
    print("🏥 Media Convert Service Health Check")
    print("=" * 40)
    
    all_healthy, results = checker.check_all()
    
    for service, result in results.items():
        status = "✅ HEALTHY" if result['healthy'] else "❌ UNHEALTHY"
        print(f"{service.upper()}: {status}")
        print(f"  Message: {result['message']}")
        print()
    
    if all_healthy:
        print("🎉 All services are healthy!")
        logger.info("Health check passed", extra={"results": results})
        return 0
    else:
        print("❌ Some services are unhealthy!")
        logger.error("Health check failed", extra={"results": results})
        return 1

if __name__ == "__main__":
    sys.exit(main())
