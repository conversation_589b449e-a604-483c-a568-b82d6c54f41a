"""
Health Check Module for Media Convert Service

This module provides health check functionality for the Celery worker
and can be used by Kubernetes probes or monitoring systems.
"""

import sys
import time
import redis
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class HealthChecker:
    """Health checker for Media Convert Service components."""
    
    def __init__(self):
        self.redis_host = config('REDIS_HOST', default='localhost')
        self.redis_port = config('REDIS_PORT', default=6379, cast=int)
        self.redis_db = config('REDIS_DB', default=0, cast=int)
    
    def check_redis(self):
        """Check Redis connectivity."""
        try:
            r = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                socket_timeout=5
            )
            r.ping()
            return True, "Redis is healthy"
        except Exception as e:
            return False, f"Redis check failed: {e}"
    
    def check_redis_broker(self):
        """Check Redis as Celery broker."""
        try:
            r = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                socket_timeout=5
            )

            # Test broker functionality
            test_key = 'health_check_broker_test'
            r.lpush(test_key, 'test')
            result = r.rpop(test_key)

            if result == b'test':
                return True, "Redis broker is healthy"
            else:
                return False, "Redis broker test failed"
        except Exception as e:
            return False, f"Redis broker check failed: {e}"
    
    def check_celery(self):
        """Check Celery worker status."""
        try:
            from app.celery_app import celery_app
            
            # Check if we can create a Celery instance
            inspect = celery_app.control.inspect()
            
            # Try to get worker stats (this will work even if no workers are running)
            stats = inspect.stats()
            
            return True, "Celery is healthy"
        except Exception as e:
            return False, f"Celery check failed: {e}"
    
    def check_all(self):
        """Run all health checks."""
        checks = {
            'redis': self.check_redis,
            'redis_broker': self.check_redis_broker,
            'celery': self.check_celery
        }
        
        results = {}
        all_healthy = True
        
        for name, check_func in checks.items():
            try:
                healthy, message = check_func()
                results[name] = {
                    'healthy': healthy,
                    'message': message
                }
                if not healthy:
                    all_healthy = False
            except Exception as e:
                results[name] = {
                    'healthy': False,
                    'message': f"Health check error: {e}"
                }
                all_healthy = False
        
        return all_healthy, results

def main():
    """Main health check function for command line usage."""
    checker = HealthChecker()
    
    print("🏥 Media Convert Service Health Check")
    print("=" * 40)
    
    all_healthy, results = checker.check_all()
    
    for service, result in results.items():
        status = "✅ HEALTHY" if result['healthy'] else "❌ UNHEALTHY"
        print(f"{service.upper()}: {status}")
        print(f"  Message: {result['message']}")
        print()
    
    if all_healthy:
        print("🎉 All services are healthy!")
        logger.info("Health check passed", extra={"results": results})
        return 0
    else:
        print("❌ Some services are unhealthy!")
        logger.error("Health check failed", extra={"results": results})
        return 1

if __name__ == "__main__":
    sys.exit(main())
