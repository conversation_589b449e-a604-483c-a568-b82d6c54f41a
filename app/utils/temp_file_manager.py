"""
Temporary File Manager for Media Convert Service

This module provides utilities for managing temporary files and directories
with automatic cleanup and monitoring.
"""

import os
import shutil
import tempfile
import time
from pathlib import Path
from typing import Optional, List
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class TempFileManager:
    """Manager for temporary files and directories with automatic cleanup."""
    
    def __init__(self):
        """Initialize temporary file manager."""
        self.temp_base_dir = config('TEMP_DIR', default='/tmp/media-convert')
        self.max_age = config('MAX_TEMP_FILE_AGE', default=3600, cast=int)  # 1 hour
        
        # Ensure base directory exists
        os.makedirs(self.temp_base_dir, exist_ok=True)
        
        logger.info("Temp file manager initialized", extra={
            "temp_base_dir": self.temp_base_dir,
            "max_age": self.max_age
        })
    
    def create_temp_dir(self, prefix: str = "media_convert_", task_id: str = None) -> str:
        """
        Create a temporary directory.
        
        Args:
            prefix (str): Prefix for directory name
            task_id (str, optional): Task ID for logging
            
        Returns:
            str: Path to created temporary directory
        """
        temp_dir = tempfile.mkdtemp(prefix=prefix, dir=self.temp_base_dir)
        
        logger.info("Created temporary directory", extra={
            "task_id": task_id,
            "temp_dir": temp_dir
        })
        
        return temp_dir
    
    def cleanup_temp_dir(self, temp_dir: str, task_id: str = None) -> bool:
        """
        Clean up a temporary directory.
        
        Args:
            temp_dir (str): Path to temporary directory
            task_id (str, optional): Task ID for logging
            
        Returns:
            bool: True if cleanup was successful
        """
        try:
            if os.path.exists(temp_dir):
                # Calculate directory size before cleanup
                total_size = self._get_directory_size(temp_dir)
                
                shutil.rmtree(temp_dir)
                
                logger.info("Cleaned up temporary directory", extra={
                    "task_id": task_id,
                    "temp_dir": temp_dir,
                    "size_cleaned": total_size
                })
                
                return True
            else:
                logger.warning("Temporary directory not found for cleanup", extra={
                    "task_id": task_id,
                    "temp_dir": temp_dir
                })
                return True  # Consider it successful if already gone
                
        except Exception as e:
            logger.error("Failed to cleanup temporary directory", extra={
                "task_id": task_id,
                "temp_dir": temp_dir,
                "error": str(e)
            })
            return False
    
    def cleanup_old_files(self, task_id: str = None) -> dict:
        """
        Clean up old temporary files and directories.
        
        Args:
            task_id (str, optional): Task ID for logging
            
        Returns:
            dict: Cleanup statistics
        """
        logger.info("Starting cleanup of old temporary files", extra={
            "task_id": task_id,
            "temp_base_dir": self.temp_base_dir,
            "max_age": self.max_age
        })
        
        current_time = time.time()
        cleaned_count = 0
        cleaned_size = 0
        failed_count = 0
        
        try:
            if not os.path.exists(self.temp_base_dir):
                return {
                    "cleaned_count": 0,
                    "cleaned_size": 0,
                    "failed_count": 0,
                    "message": "Temp base directory does not exist"
                }
            
            for item in os.listdir(self.temp_base_dir):
                item_path = os.path.join(self.temp_base_dir, item)
                
                try:
                    # Get item age
                    item_age = current_time - os.path.getctime(item_path)
                    
                    if item_age > self.max_age:
                        # Calculate size before deletion
                        if os.path.isdir(item_path):
                            item_size = self._get_directory_size(item_path)
                            shutil.rmtree(item_path)
                        else:
                            item_size = os.path.getsize(item_path)
                            os.remove(item_path)
                        
                        cleaned_count += 1
                        cleaned_size += item_size
                        
                        logger.debug("Cleaned up old temp item", extra={
                            "task_id": task_id,
                            "item_path": item_path,
                            "age": item_age,
                            "size": item_size
                        })
                
                except Exception as e:
                    failed_count += 1
                    logger.warning("Failed to cleanup temp item", extra={
                        "task_id": task_id,
                        "item_path": item_path,
                        "error": str(e)
                    })
            
            result = {
                "cleaned_count": cleaned_count,
                "cleaned_size": cleaned_size,
                "failed_count": failed_count,
                "message": f"Cleaned {cleaned_count} items, {cleaned_size} bytes"
            }
            
            logger.info("Completed cleanup of old temporary files", extra={
                "task_id": task_id,
                "result": result
            })
            
            return result
            
        except Exception as e:
            logger.error("Failed to cleanup old temporary files", extra={
                "task_id": task_id,
                "error": str(e)
            })
            return {
                "cleaned_count": cleaned_count,
                "cleaned_size": cleaned_size,
                "failed_count": failed_count + 1,
                "error": str(e)
            }
    
    def get_temp_usage(self, task_id: str = None) -> dict:
        """
        Get temporary directory usage statistics.
        
        Args:
            task_id (str, optional): Task ID for logging
            
        Returns:
            dict: Usage statistics
        """
        try:
            if not os.path.exists(self.temp_base_dir):
                return {
                    "total_size": 0,
                    "total_files": 0,
                    "total_dirs": 0,
                    "message": "Temp base directory does not exist"
                }
            
            total_size = 0
            total_files = 0
            total_dirs = 0
            
            for root, dirs, files in os.walk(self.temp_base_dir):
                total_dirs += len(dirs)
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                        total_files += 1
                    except (OSError, IOError):
                        # File might have been deleted during iteration
                        pass
            
            result = {
                "total_size": total_size,
                "total_files": total_files,
                "total_dirs": total_dirs,
                "temp_base_dir": self.temp_base_dir
            }
            
            logger.debug("Temp usage statistics", extra={
                "task_id": task_id,
                "result": result
            })
            
            return result
            
        except Exception as e:
            logger.error("Failed to get temp usage statistics", extra={
                "task_id": task_id,
                "error": str(e)
            })
            return {
                "total_size": 0,
                "total_files": 0,
                "total_dirs": 0,
                "error": str(e)
            }
    
    def _get_directory_size(self, directory: str) -> int:
        """
        Calculate total size of a directory.
        
        Args:
            directory (str): Path to directory
            
        Returns:
            int: Total size in bytes
        """
        total_size = 0
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        # File might have been deleted during iteration
                        pass
        except Exception:
            # If we can't walk the directory, return 0
            pass
        
        return total_size

# Global instance
temp_manager = TempFileManager()

# Convenience functions
def create_temp_dir(prefix: str = "media_convert_", task_id: str = None) -> str:
    """Create a temporary directory."""
    return temp_manager.create_temp_dir(prefix, task_id)

def cleanup_temp_dir(temp_dir: str, task_id: str = None) -> bool:
    """Clean up a temporary directory."""
    return temp_manager.cleanup_temp_dir(temp_dir, task_id)

def cleanup_old_files(task_id: str = None) -> dict:
    """Clean up old temporary files."""
    return temp_manager.cleanup_old_files(task_id)

def get_temp_usage(task_id: str = None) -> dict:
    """Get temporary directory usage statistics."""
    return temp_manager.get_temp_usage(task_id)
