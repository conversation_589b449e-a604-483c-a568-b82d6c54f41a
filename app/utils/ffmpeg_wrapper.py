"""
FFmpeg Wrapper for Media Convert Service

This module provides a wrapper for FFmpeg to convert MP4 videos to MPEG-DASH format
with multiple bitrates for Adaptive Bitrate Streaming (ABR).
"""

import os
import subprocess
import json
import tempfile
import psutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)


class FFmpegWrapper:
    """Wrapper for FFmpeg video conversion operations."""

    def __init__(self):
        """Initialize FFmpeg wrapper with configuration."""
        self.ffmpeg_path = config("FFMPEG_PATH", default="ffmpeg")
        self.ffprobe_path = config("FFMPEG_PROBE_PATH", default="ffprobe")

        # Verify FFmpeg installation
        self._verify_ffmpeg()

        logger.info(
            "FFmpeg wrapper initialized",
            extra={"ffmpeg_path": self.ffmpeg_path, "ffprobe_path": self.ffprobe_path},
        )

    def _verify_ffmpeg(self):
        """Verify that <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> are available."""
        try:
            # Check FFmpeg
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg not working: {result.stderr}")

            # Check FFprobe
            result = subprocess.run(
                [self.ffprobe_path, "-version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe not working: {result.stderr}")

            logger.info("FFmpeg verification successful")

        except FileNotFoundError as e:
            raise RuntimeError(f"FFmpeg not found. Please install FFmpeg: {e}")
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg verification timed out")

    def get_video_info(self, input_path: str, task_id: str = None) -> Dict[str, Any]:
        """
        Get video information using FFprobe.

        Args:
            input_path (str): Path to input video file
            task_id (str, optional): Task ID for logging

        Returns:
            dict: Video information including duration, resolution, bitrate, etc.
        """
        logger.info(
            "Getting video info", extra={"task_id": task_id, "input_path": input_path}
        )

        try:
            cmd = [
                self.ffprobe_path,
                "-v",
                "quiet",
                "-print_format",
                "json",
                "-show_format",
                "-show_streams",
                input_path,
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                raise RuntimeError(f"FFprobe failed: {result.stderr}")

            probe_data = json.loads(result.stdout)

            # Extract video stream info
            video_stream = None
            audio_stream = None

            for stream in probe_data.get("streams", []):
                if stream.get("codec_type") == "video" and video_stream is None:
                    video_stream = stream
                elif stream.get("codec_type") == "audio" and audio_stream is None:
                    audio_stream = stream

            if not video_stream:
                raise ValueError("No video stream found in input file")

            # Extract relevant information
            info = {
                "duration": float(probe_data.get("format", {}).get("duration", 0)),
                "size": int(probe_data.get("format", {}).get("size", 0)),
                "bitrate": int(probe_data.get("format", {}).get("bit_rate", 0)),
                "format_name": probe_data.get("format", {}).get("format_name", ""),
                "video": {
                    "codec": video_stream.get("codec_name", ""),
                    "width": int(video_stream.get("width", 0)),
                    "height": int(video_stream.get("height", 0)),
                    "fps": eval(video_stream.get("r_frame_rate", "0/1")),
                    "bitrate": int(video_stream.get("bit_rate", 0))
                    if video_stream.get("bit_rate")
                    else None,
                },
                "audio": {
                    "codec": audio_stream.get("codec_name", "")
                    if audio_stream
                    else None,
                    "channels": int(audio_stream.get("channels", 0))
                    if audio_stream
                    else 0,
                    "sample_rate": int(audio_stream.get("sample_rate", 0))
                    if audio_stream
                    else 0,
                    "bitrate": int(audio_stream.get("bit_rate", 0))
                    if audio_stream and audio_stream.get("bit_rate")
                    else None,
                }
                if audio_stream
                else None,
            }

            logger.info(
                "Video info extracted", extra={"task_id": task_id, "info": info}
            )

            return info

        except subprocess.TimeoutExpired:
            raise RuntimeError("FFprobe timed out")
        except json.JSONDecodeError as e:
            raise RuntimeError(f"Failed to parse FFprobe output: {e}")
        except Exception as e:
            logger.error(
                "Failed to get video info",
                extra={"task_id": task_id, "error": str(e), "input_path": input_path},
            )
            raise

    def convert_to_dash(
        self,
        input_path: str,
        output_dir: str,
        task_id: str = None,
        bitrates: List[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Convert MP4 video to MPEG-DASH format with multiple bitrates.

        Args:
            input_path (str): Path to input MP4 file
            output_dir (str): Directory to save DASH files
            task_id (str, optional): Task ID for logging
            bitrates (list, optional): List of bitrate configurations

        Returns:
            dict: Conversion result with output files and metadata
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Default bitrate configurations optimized for LMS/Educational content
        if bitrates is None:
            bitrates = [
                {
                    "resolution": "1280x720",
                    "video_bitrate": "2500k",
                    "audio_bitrate": "96k",
                    "name": "720p",
                    "id": "1",
                },
                {
                    "resolution": "854x480",
                    "video_bitrate": "1000k",
                    "audio_bitrate": "96k",
                    "name": "480p",
                    "id": "2",
                },
                {
                    "resolution": "640x360",
                    "video_bitrate": "600k",
                    "audio_bitrate": "96k",
                    "name": "360p",
                    "id": "3",
                },
                {
                    "resolution": "426x240",
                    "video_bitrate": "300k",
                    "audio_bitrate": "96k",
                    "name": "240p",
                    "id": "4",
                },
            ]

        logger.info(
            "Starting DASH conversion",
            extra={
                "task_id": task_id,
                "input_path": input_path,
                "output_dir": output_dir,
                "bitrates": bitrates,
            },
        )

        try:
            # Get input video info
            video_info = self.get_video_info(input_path, task_id)

            # Log system resources for EKS monitoring
            logger.info(
                "System resources for DASH conversion",
                extra={
                    "task_id": task_id,
                    "video_duration": video_info["duration"],
                    "video_resolution": f"{video_info['video']['width']}x{video_info['video']['height']}",
                    "system_cpu_count": os.cpu_count(),
                    "system_memory_gb": round(
                        psutil.virtual_memory().total / (1024**3), 2
                    ),
                    "available_memory_gb": round(
                        psutil.virtual_memory().available / (1024**3), 2
                    ),
                },
            )

            # Filter bitrates based on input resolution (no upscaling)
            input_height = video_info["video"]["height"]
            input_width = video_info["video"]["width"]

            # Only include resolutions that are same size or smaller (no upscaling)
            suitable_bitrates = []
            for br in bitrates:
                target_width, target_height = br["resolution"].split("x")
                target_h = int(target_height)
                target_w = int(target_width)

                if target_h <= input_height and target_w <= input_width:
                    suitable_bitrates.append(br)
                else:
                    logger.info(
                        f"Skipping {br['name']} - would require upscaling",
                        extra={
                            "task_id": task_id,
                            "target_resolution": br["resolution"],
                            "input_resolution": f"{input_width}x{input_height}",
                        },
                    )

            # If no suitable bitrates, use the smallest one as fallback
            filtered_bitrates = (
                suitable_bitrates if suitable_bitrates else [bitrates[-1]]
            )

            logger.info(
                "Filtered bitrates for LMS optimization",
                extra={
                    "task_id": task_id,
                    "input_resolution": f"{input_width}x{input_height}",
                    "filtered_bitrates": [
                        f"{br['name']} ({br['resolution']})" for br in filtered_bitrates
                    ],
                },
            )

            # Generate DASH with proper segmentation using single FFmpeg command
            # This approach creates proper .m4s segments and init files

            # Extract base filename for consistent naming
            base_name = Path(input_path).stem
            manifest_path = os.path.join(output_dir, "manifest.mpd")

            # Build comprehensive FFmpeg command optimized for EKS environment
            ffmpeg_threads = config("FFMPEG_THREADS", default="2")
            ffmpeg_preset = config("FFMPEG_PRESET", default="medium")

            cmd = [
                self.ffmpeg_path,
                "-i",
                input_path,
                "-y",  # Overwrite output files
                "-threads",
                ffmpeg_threads,  # Configurable thread count
                "-preset",
                ffmpeg_preset,  # Configurable preset for CPU/quality balance
                "-tune",
                "zerolatency",  # Optimize for streaming
            ]

            # Use all available filtered bitrates for full multi-bitrate DASH
            selected_bitrates = filtered_bitrates

            logger.info(
                f"Using {len(selected_bitrates)} video bitrates for adaptive streaming",
                extra={
                    "task_id": task_id,
                    "selected_bitrates": [
                        f"{br['name']} ({br['resolution']}) @ {br['video_bitrate']}"
                        for br in selected_bitrates
                    ],
                },
            )

            # Add video streams for each selected bitrate
            for i, bitrate_config in enumerate(selected_bitrates):
                cmd.extend(
                    [
                        "-map",
                        "0:v:0",  # Map video stream
                        f"-c:v:{i}",
                        "libx264",
                        f"-b:v:{i}",
                        bitrate_config["video_bitrate"],
                        f"-s:v:{i}",
                        bitrate_config["resolution"],
                        f"-profile:v:{i}",
                        "high",
                        f"-level:v:{i}",
                        "4.0",
                        f"-g:v:{i}",
                        "72",  # GOP size
                        f"-keyint_min:v:{i}",
                        "72",
                        f"-sc_threshold:v:{i}",
                        "0",  # Disable scene cut detection
                        f"-force_key_frames:v:{i}",
                        "expr:gte(t,n_forced*6)",  # Force keyframes every 6 seconds
                    ]
                )

                logger.info(
                    f"Adding {bitrate_config['name']} video stream",
                    extra={
                        "task_id": task_id,
                        "stream_index": i,
                        "resolution": bitrate_config["resolution"],
                        "bitrate": bitrate_config["video_bitrate"],
                    },
                )

            # Add audio stream (if audio exists)
            if video_info["audio"]:
                cmd.extend(
                    [
                        "-map",
                        "0:a:0",  # Map audio stream
                        "-c:a",
                        "aac",
                        "-b:a",
                        selected_bitrates[0][
                            "audio_bitrate"
                        ],  # Use first bitrate for audio
                        "-ac",
                        "2" if video_info["audio"]["channels"] > 1 else "1",
                        "-ar",
                        "48000",  # Standard sample rate for DASH
                    ]
                )

                logger.info(
                    "Adding audio stream",
                    extra={
                        "task_id": task_id,
                        "bitrate": selected_bitrates[0]["audio_bitrate"],
                    },
                )

            # DASH-specific options for proper segmentation
            # Create separate adaptation sets for each video stream to avoid aspect ratio conflicts
            video_adaptation_sets = []
            for i in range(len(selected_bitrates)):
                video_adaptation_sets.append(f"id={i},streams={i}")

            # Add audio adaptation set
            audio_adaptation_set = (
                f"id={len(selected_bitrates)},streams=a" if video_info["audio"] else ""
            )

            # Combine all adaptation sets
            if audio_adaptation_set:
                adaptation_sets_str = (
                    " ".join(video_adaptation_sets) + " " + audio_adaptation_set
                )
            else:
                adaptation_sets_str = " ".join(video_adaptation_sets)

            # Simplify: Use the working approach first, then add thumbnails separately
            cmd.extend(
                [
                    "-f",
                    "dash",
                    "-seg_duration",
                    "6",  # 6-second segments (like AWS MediaConvert)
                    "-use_template",
                    "1",
                    "-use_timeline",
                    "0",  # Use number-based segments
                    "-adaptation_sets",
                    adaptation_sets_str,
                    "-init_seg_name",
                    f"{base_name}_$RepresentationID$init.m4s",
                    "-media_seg_name",
                    f"{base_name}_$RepresentationID$_$Number%09d$.m4s",
                    "-single_file",
                    "0",  # Generate separate segment files
                    "-streaming",
                    "1",  # Optimize for streaming
                    "manifest.mpd",  # Use FFmpeg-generated manifest
                ]
            )

            logger.info(
                "DASH adaptation sets configured",
                extra={
                    "task_id": task_id,
                    "adaptation_sets": adaptation_sets_str,
                    "video_streams": len(selected_bitrates),
                    "audio_streams": 1 if video_info["audio"] else 0,
                },
            )

            logger.info(
                "Creating DASH manifest",
                extra={
                    "task_id": task_id,
                    "command": " ".join(cmd),
                    "output_dir": output_dir,
                    "expected_segments": int(video_info["duration"] / 6)
                    + 1,  # 6-second segments
                },
            )

            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=1800, cwd=output_dir
            )

            # Always log the FFmpeg output for debugging
            logger.info(
                "FFmpeg DASH execution completed",
                extra={
                    "task_id": task_id,
                    "return_code": result.returncode,
                    "stdout_length": len(result.stdout),
                    "stderr_length": len(result.stderr),
                },
            )

            if result.returncode != 0:
                # If FFmpeg DASH creation fails, log error and raise
                error_msg = (
                    f"FFmpeg DASH creation failed with return code {result.returncode}"
                )
                print(f"🚨 {error_msg}")
                print(f"📝 Command: {' '.join(cmd)}")
                print(f"📤 STDOUT: {result.stdout}")
                print(f"📥 STDERR: {result.stderr}")

                logger.error(
                    "FFmpeg DASH creation failed",
                    extra={
                        "task_id": task_id,
                        "ffmpeg_command": " ".join(cmd),
                        "ffmpeg_stdout": result.stdout,
                        "ffmpeg_stderr": result.stderr,
                        "return_code": result.returncode,
                    },
                )
                raise RuntimeError(
                    f"FFmpeg DASH creation failed with return code {result.returncode}"
                )

            # FFmpeg succeeded - verify manifest was created
            logger.info(
                "FFmpeg DASH creation succeeded",
                extra={
                    "task_id": task_id,
                    "output_dir": output_dir,
                    "manifest_path": manifest_path,
                },
            )

            # Generate thumbnails separately (simpler approach)
            thumbnail_count = self._generate_simple_thumbnails(
                input_path, output_dir, base_name, task_id
            )

            # Add thumbnail AdaptationSet to MPD
            if thumbnail_count > 0:
                self._add_thumbnails_to_mpd(
                    manifest_path, thumbnail_count, base_name, task_id
                )

            # Verify output files
            if not os.path.exists(manifest_path):
                raise RuntimeError("DASH manifest file was not created")

            # Collect output files
            output_files = []
            total_size = 0

            for file_path in Path(output_dir).rglob("*"):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    output_files.append(
                        {
                            "path": str(file_path),
                            "name": file_path.name,
                            "size": file_size,
                            "relative_path": str(file_path.relative_to(output_dir)),
                        }
                    )
                    total_size += file_size

            result = {
                "status": "success",
                "input_path": input_path,
                "output_dir": output_dir,
                "manifest_path": manifest_path,
                "bitrates_used": filtered_bitrates,
                "output_files": output_files,
                "total_files": len(output_files),
                "total_size": total_size,
                "input_info": video_info,
                "ffmpeg_stdout": result.stdout,
                "ffmpeg_stderr": result.stderr,
            }

            logger.info(
                "DASH conversion completed",
                extra={
                    "task_id": task_id,
                    "result": {
                        k: v
                        for k, v in result.items()
                        if k not in ["ffmpeg_stdout", "ffmpeg_stderr"]
                    },
                },
            )

            return result

        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg conversion timed out")
        except Exception as e:
            logger.error(
                "DASH conversion failed",
                extra={"task_id": task_id, "error": str(e), "input_path": input_path},
            )
            raise

    def validate_input_file(self, input_path: str, task_id: str = None) -> bool:
        """
        Validate input video file.

        Args:
            input_path (str): Path to input file
            task_id (str, optional): Task ID for logging

        Returns:
            bool: True if file is valid

        Raises:
            ValueError: If file is invalid
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")

        try:
            info = self.get_video_info(input_path, task_id)

            # Basic validation
            if info["duration"] <= 0:
                raise ValueError("Video has no duration")

            if info["video"]["width"] <= 0 or info["video"]["height"] <= 0:
                raise ValueError("Invalid video resolution")

            # Check maximum duration (2 hours)
            try:
                max_duration_str = config("MAX_VIDEO_DURATION", default="7200")
                # Remove any comments and whitespace
                max_duration_str = max_duration_str.split("#")[0].strip()
                max_duration = int(max_duration_str)
            except (ValueError, TypeError):
                max_duration = 7200  # Default to 2 hours
                logger.warning(
                    "Invalid MAX_VIDEO_DURATION config, using default",
                    extra={"task_id": task_id, "default_duration": max_duration},
                )

            if info["duration"] > max_duration:
                raise ValueError(
                    f"Video too long: {info['duration']}s (max: {max_duration}s)"
                )

            # Check supported formats
            supported_formats_str = config(
                "SUPPORTED_INPUT_FORMATS", default="mp4,avi,mov,mkv"
            )
            supported_formats = [
                fmt.strip() for fmt in supported_formats_str.split(",")
            ]

            # FFprobe can return multiple formats separated by commas (e.g., "mov,mp4,m4a,3gp,3g2,mj2")
            # Check if any of the detected formats is in our supported list
            detected_formats = [fmt.strip() for fmt in info["format_name"].split(",")]

            format_supported = any(fmt in supported_formats for fmt in detected_formats)
            if not format_supported:
                raise ValueError(
                    f"Unsupported format: {info['format_name']} (supported: {', '.join(supported_formats)})"
                )

            logger.info(
                "Input file validation passed",
                extra={
                    "task_id": task_id,
                    "input_path": input_path,
                    "duration": info["duration"],
                    "resolution": f"{info['video']['width']}x{info['video']['height']}",
                },
            )

            return True

        except Exception as e:
            logger.error(
                "Input file validation failed",
                extra={"task_id": task_id, "error": str(e), "input_path": input_path},
            )
            raise

    def _generate_simple_thumbnails(
        self, input_path: str, output_dir: str, base_name: str, task_id: str = None
    ) -> int:
        """Generate simple thumbnails every 10 seconds (AWS MediaConvert style)."""
        try:
            thumbnail_pattern = os.path.join(
                output_dir, f"{base_name}_thumbnail_%04d.jpg"
            )

            cmd = [
                self.ffmpeg_path,
                "-i",
                input_path,
                "-vf",
                "fps=1/10,scale=320:180",  # 1 frame every 10 seconds, 320x180
                "-q:v",
                "3",  # Good quality
                "-y",  # Overwrite existing files
                thumbnail_pattern,
            ]

            logger.info(
                "Generating thumbnails",
                extra={
                    "task_id": task_id,
                    "thumbnail_pattern": thumbnail_pattern,
                    "command": " ".join(cmd),
                },
            )

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Count generated thumbnails
                thumbnail_files = [
                    f
                    for f in os.listdir(output_dir)
                    if f.startswith(f"{base_name}_thumbnail_") and f.endswith(".jpg")
                ]
                logger.info(
                    "Thumbnails generated successfully",
                    extra={
                        "task_id": task_id,
                        "thumbnail_count": len(thumbnail_files),
                        "files": thumbnail_files[:5],  # Log first 5 files
                    },
                )
                return len(thumbnail_files)
            else:
                logger.warning(
                    "Thumbnail generation failed, continuing without thumbnails",
                    extra={
                        "task_id": task_id,
                        "command": " ".join(cmd),
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "return_code": result.returncode,
                    },
                )
                return 0

        except Exception as e:
            logger.warning(
                "Thumbnail generation failed with exception",
                extra={"task_id": task_id, "error": str(e)},
            )
            return 0

    def _add_thumbnails_to_mpd(
        self,
        manifest_path: str,
        thumbnail_count: int,
        base_name: str,
        task_id: str = None,
    ):
        """Add thumbnail AdaptationSet to existing MPD file."""
        try:
            import xml.etree.ElementTree as ET

            # Parse existing MPD
            tree = ET.parse(manifest_path)
            root = tree.getroot()

            # Find the Period element
            period = root.find(".//{urn:mpeg:dash:schema:mpd:2011}Period")
            if period is None:
                logger.warning(
                    "Could not find Period element in MPD", extra={"task_id": task_id}
                )
                return

            # Get the next AdaptationSet ID (should be 5 after 4 video + 1 audio)
            existing_adaptation_sets = period.findall(
                ".//{urn:mpeg:dash:schema:mpd:2011}AdaptationSet"
            )
            next_id = len(existing_adaptation_sets)

            # Create thumbnail AdaptationSet
            thumbnail_adaptation_set = ET.SubElement(period, "AdaptationSet")
            thumbnail_adaptation_set.set("id", str(next_id))
            thumbnail_adaptation_set.set("contentType", "image")
            thumbnail_adaptation_set.set("mimeType", "image/jpeg")
            thumbnail_adaptation_set.set("startWithSAP", "1")
            thumbnail_adaptation_set.set("segmentAlignment", "true")

            # Create Representation for thumbnails
            representation = ET.SubElement(thumbnail_adaptation_set, "Representation")
            representation.set("id", f"thumbnails_{next_id}")
            representation.set("bandwidth", "27322")  # Typical thumbnail bandwidth
            representation.set("width", "320")
            representation.set("height", "180")

            # Create SegmentTemplate for thumbnails
            segment_template = ET.SubElement(representation, "SegmentTemplate")
            segment_template.set("media", f"{base_name}_thumbnail_$Number%04d$.jpg")
            segment_template.set("duration", "100000")  # 10 seconds in 10000 timescale
            segment_template.set("timescale", "10000")
            segment_template.set("startNumber", "1")

            # Add EssentialProperty for thumbnail specification (optional but good practice)
            essential_property = ET.SubElement(representation, "EssentialProperty")
            essential_property.set(
                "schemeIdUri", "http://dashif.org/guidelines/thumbnail_tile"
            )
            essential_property.set("value", "1x1")  # Single thumbnail per segment

            # Write modified MPD back to file
            # Add XML declaration and proper formatting
            ET.register_namespace("", "urn:mpeg:dash:schema:mpd:2011")
            tree.write(manifest_path, encoding="utf-8", xml_declaration=True)

            logger.info(
                "Added thumbnail AdaptationSet to MPD",
                extra={
                    "task_id": task_id,
                    "manifest_path": manifest_path,
                    "thumbnail_count": thumbnail_count,
                    "adaptation_set_id": next_id,
                },
            )

        except Exception as e:
            logger.warning(
                "Failed to add thumbnails to MPD",
                extra={
                    "task_id": task_id,
                    "manifest_path": manifest_path,
                    "error": str(e),
                },
            )
