"""
FFmpeg Wrapper for Media Convert Service

This module provides a wrapper for FFmpeg to convert MP4 videos to MPEG-DASH format
with multiple bitrates for Adaptive Bitrate Streaming (ABR).
"""

import os
import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class FFmpegWrapper:
    """Wrapper for FFmpeg video conversion operations."""
    
    def __init__(self):
        """Initialize FFmpeg wrapper with configuration."""
        self.ffmpeg_path = config('FFMPEG_PATH', default='ffmpeg')
        self.ffprobe_path = config('FFMPEG_PROBE_PATH', default='ffprobe')
        
        # Verify FFmpeg installation
        self._verify_ffmpeg()
        
        logger.info("FFmpeg wrapper initialized", extra={
            "ffmpeg_path": self.ffmpeg_path,
            "ffprobe_path": self.ffprobe_path
        })
    
    def _verify_ffmpeg(self):
        """Verify that <PERSON>mpeg and <PERSON><PERSON><PERSON> are available."""
        try:
            # Check FFmpeg
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg not working: {result.stderr}")
            
            # Check FFprobe
            result = subprocess.run([self.ffprobe_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe not working: {result.stderr}")
                
            logger.info("FFmpeg verification successful")
            
        except FileNotFoundError as e:
            raise RuntimeError(f"FFmpeg not found. Please install FFmpeg: {e}")
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg verification timed out")
    
    def get_video_info(self, input_path: str, task_id: str = None) -> Dict[str, Any]:
        """
        Get video information using FFprobe.
        
        Args:
            input_path (str): Path to input video file
            task_id (str, optional): Task ID for logging
            
        Returns:
            dict: Video information including duration, resolution, bitrate, etc.
        """
        logger.info("Getting video info", extra={
            "task_id": task_id,
            "input_path": input_path
        })
        
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe failed: {result.stderr}")
            
            probe_data = json.loads(result.stdout)
            
            # Extract video stream info
            video_stream = None
            audio_stream = None
            
            for stream in probe_data.get('streams', []):
                if stream.get('codec_type') == 'video' and video_stream is None:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and audio_stream is None:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found in input file")
            
            # Extract relevant information
            info = {
                "duration": float(probe_data.get('format', {}).get('duration', 0)),
                "size": int(probe_data.get('format', {}).get('size', 0)),
                "bitrate": int(probe_data.get('format', {}).get('bit_rate', 0)),
                "format_name": probe_data.get('format', {}).get('format_name', ''),
                "video": {
                    "codec": video_stream.get('codec_name', ''),
                    "width": int(video_stream.get('width', 0)),
                    "height": int(video_stream.get('height', 0)),
                    "fps": eval(video_stream.get('r_frame_rate', '0/1')),
                    "bitrate": int(video_stream.get('bit_rate', 0)) if video_stream.get('bit_rate') else None
                },
                "audio": {
                    "codec": audio_stream.get('codec_name', '') if audio_stream else None,
                    "channels": int(audio_stream.get('channels', 0)) if audio_stream else 0,
                    "sample_rate": int(audio_stream.get('sample_rate', 0)) if audio_stream else 0,
                    "bitrate": int(audio_stream.get('bit_rate', 0)) if audio_stream and audio_stream.get('bit_rate') else None
                } if audio_stream else None
            }
            
            logger.info("Video info extracted", extra={
                "task_id": task_id,
                "info": info
            })
            
            return info
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFprobe timed out")
        except json.JSONDecodeError as e:
            raise RuntimeError(f"Failed to parse FFprobe output: {e}")
        except Exception as e:
            logger.error("Failed to get video info", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise
    
    def convert_to_dash(self, input_path: str, output_dir: str, task_id: str = None,
                       bitrates: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Convert MP4 video to MPEG-DASH format with multiple bitrates.
        
        Args:
            input_path (str): Path to input MP4 file
            output_dir (str): Directory to save DASH files
            task_id (str, optional): Task ID for logging
            bitrates (list, optional): List of bitrate configurations
            
        Returns:
            dict: Conversion result with output files and metadata
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Default bitrate configurations optimized for LMS/Educational content
        if bitrates is None:
            bitrates = [
                {"resolution": "1280x720", "video_bitrate": "2500k", "audio_bitrate": "96k", "name": "720p", "id": "1"},
                {"resolution": "854x480", "video_bitrate": "1000k", "audio_bitrate": "96k", "name": "480p", "id": "2"},
                {"resolution": "640x360", "video_bitrate": "600k", "audio_bitrate": "96k", "name": "360p", "id": "3"},
                {"resolution": "426x240", "video_bitrate": "300k", "audio_bitrate": "96k", "name": "240p", "id": "4"}
            ]
        
        logger.info("Starting DASH conversion", extra={
            "task_id": task_id,
            "input_path": input_path,
            "output_dir": output_dir,
            "bitrates": bitrates
        })
        
        try:
            # Get input video info
            video_info = self.get_video_info(input_path, task_id)
            
            # Filter bitrates based on input resolution (no upscaling)
            input_height = video_info["video"]["height"]
            input_width = video_info["video"]["width"]

            # Only include resolutions that are same size or smaller (no upscaling)
            suitable_bitrates = []
            for br in bitrates:
                target_width, target_height = br["resolution"].split('x')
                target_h = int(target_height)
                target_w = int(target_width)

                if target_h <= input_height and target_w <= input_width:
                    suitable_bitrates.append(br)
                else:
                    logger.info(f"Skipping {br['name']} - would require upscaling", extra={
                        "task_id": task_id,
                        "target_resolution": br["resolution"],
                        "input_resolution": f"{input_width}x{input_height}"
                    })

            # If no suitable bitrates, use the smallest one as fallback
            filtered_bitrates = suitable_bitrates if suitable_bitrates else [bitrates[-1]]

            logger.info("Filtered bitrates for LMS optimization", extra={
                "task_id": task_id,
                "input_resolution": f"{input_width}x{input_height}",
                "filtered_bitrates": [f"{br['name']} ({br['resolution']})" for br in filtered_bitrates]
            })
            
            # Generate DASH with proper segmentation using single FFmpeg command
            # This approach creates proper .m4s segments and init files

            manifest_path = os.path.join(output_dir, "manifest.mpd")

            # Extract base filename for consistent naming
            base_name = Path(input_path).stem

            # Build comprehensive FFmpeg command for DASH with multiple bitrates
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-y',  # Overwrite output files
            ]

            # Use single bitrate for now (what was working)
            best_bitrate = filtered_bitrates[0]

            cmd.extend([
                '-map', '0:v:0',  # Map video stream
                '-c:v', 'libx264',
                '-b:v', best_bitrate["video_bitrate"],
                '-s', best_bitrate["resolution"],
                '-profile:v', 'high',
                '-level', '4.0',
                '-g', '72',  # GOP size
                '-keyint_min', '72',
                '-sc_threshold', '0',  # Disable scene cut detection
                '-force_key_frames', 'expr:gte(t,n_forced*6)',  # Force keyframes every 6 seconds
            ])

            logger.info(f"Adding {best_bitrate['name']} video stream", extra={
                "task_id": task_id,
                "resolution": best_bitrate["resolution"],
                "bitrate": best_bitrate["video_bitrate"]
            })

            # Add audio stream (if audio exists)
            if video_info["audio"]:
                cmd.extend([
                    '-map', '0:a:0',  # Map audio stream
                    '-c:a', 'aac',
                    '-b:a', best_bitrate["audio_bitrate"],
                    '-ac', '2' if video_info["audio"]["channels"] > 1 else '1',
                    '-ar', '48000',  # Standard sample rate for DASH
                ])

                logger.info("Adding audio stream", extra={
                    "task_id": task_id,
                    "bitrate": best_bitrate["audio_bitrate"]
                })

            # DASH-specific options for proper segmentation
            cmd.extend([
                '-f', 'dash',
                '-seg_duration', '6',  # 6-second segments (like AWS MediaConvert)
                '-use_template', '1',
                '-use_timeline', '0',  # Use number-based segments
                '-adaptation_sets', 'id=0,streams=v id=1,streams=a' if video_info["audio"] else 'id=0,streams=v',
                '-init_seg_name', f'{base_name}_$RepresentationID$init.m4s',
                '-media_seg_name', f'{base_name}_$RepresentationID$_$Number%09d$.m4s',
                '-single_file', '0',  # Generate separate segment files
                '-streaming', '1',  # Optimize for streaming
                manifest_path
            ])

            logger.info("Creating DASH manifest", extra={
                "task_id": task_id,
                "command": " ".join(cmd),
                "output_dir": output_dir,
                "expected_segments": int(video_info["duration"] / 6) + 1  # 6-second segments
            })

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)

            # Always log the FFmpeg output for debugging
            logger.info("FFmpeg DASH execution completed", extra={
                "task_id": task_id,
                "return_code": result.returncode,
                "stdout_length": len(result.stdout),
                "stderr_length": len(result.stderr)
            })

            if result.returncode != 0:
                # If FFmpeg DASH creation fails, create custom MPD
                error_msg = f"FFmpeg DASH failed with return code {result.returncode}"
                print(f"🚨 {error_msg}")
                print(f"📝 Command: {' '.join(cmd)}")
                print(f"📤 STDOUT: {result.stdout}")
                print(f"📥 STDERR: {result.stderr}")

                logger.error("FFmpeg DASH creation failed", extra={
                    "task_id": task_id,
                    "ffmpeg_command": " ".join(cmd),
                    "ffmpeg_stdout": result.stdout,
                    "ffmpeg_stderr": result.stderr,
                    "return_code": result.returncode
                })
                manifest_path = self.create_custom_mpd(output_dir, base_name, video_info, filtered_bitrates, task_id)
            else:
                # Log successful execution details
                logger.info("FFmpeg DASH creation succeeded", extra={
                    "task_id": task_id,
                    "ffmpeg_stdout": result.stdout[-500:] if result.stdout else "",  # Last 500 chars
                    "ffmpeg_stderr": result.stderr[-500:] if result.stderr else ""   # Last 500 chars
                })
                manifest_path = os.path.join(output_dir, f"{base_name}.mpd")

                # Check what files were actually created
                created_files = []
                if os.path.exists(output_dir):
                    created_files = os.listdir(output_dir)

                logger.info("Files created by FFmpeg DASH", extra={
                    "task_id": task_id,
                    "output_dir": output_dir,
                    "created_files": created_files,
                    "file_count": len(created_files)
                })

            # Verify output files
            if not os.path.exists(manifest_path):
                raise RuntimeError("DASH manifest file was not created")
            
            # Collect output files
            output_files = []
            total_size = 0
            
            for file_path in Path(output_dir).rglob('*'):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    output_files.append({
                        "path": str(file_path),
                        "name": file_path.name,
                        "size": file_size,
                        "relative_path": str(file_path.relative_to(output_dir))
                    })
                    total_size += file_size
            
            result = {
                "status": "success",
                "input_path": input_path,
                "output_dir": output_dir,
                "manifest_path": manifest_path,
                "bitrates_used": filtered_bitrates,
                "output_files": output_files,
                "total_files": len(output_files),
                "total_size": total_size,
                "input_info": video_info,
                "ffmpeg_stdout": result.stdout,
                "ffmpeg_stderr": result.stderr
            }
            
            logger.info("DASH conversion completed", extra={
                "task_id": task_id,
                "result": {k: v for k, v in result.items() if k not in ["ffmpeg_stdout", "ffmpeg_stderr"]}
            })
            
            return result
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg conversion timed out")
        except Exception as e:
            logger.error("DASH conversion failed", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise
    
    def validate_input_file(self, input_path: str, task_id: str = None) -> bool:
        """
        Validate input video file.
        
        Args:
            input_path (str): Path to input file
            task_id (str, optional): Task ID for logging
            
        Returns:
            bool: True if file is valid
            
        Raises:
            ValueError: If file is invalid
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        try:
            info = self.get_video_info(input_path, task_id)
            
            # Basic validation
            if info["duration"] <= 0:
                raise ValueError("Video has no duration")
            
            if info["video"]["width"] <= 0 or info["video"]["height"] <= 0:
                raise ValueError("Invalid video resolution")
            
            # Check maximum duration (2 hours)
            try:
                max_duration_str = config('MAX_VIDEO_DURATION', default='7200')
                # Remove any comments and whitespace
                max_duration_str = max_duration_str.split('#')[0].strip()
                max_duration = int(max_duration_str)
            except (ValueError, TypeError):
                max_duration = 7200  # Default to 2 hours
                logger.warning("Invalid MAX_VIDEO_DURATION config, using default", extra={
                    "task_id": task_id,
                    "default_duration": max_duration
                })

            if info["duration"] > max_duration:
                raise ValueError(f"Video too long: {info['duration']}s (max: {max_duration}s)")
            
            # Check supported formats
            supported_formats_str = config('SUPPORTED_INPUT_FORMATS', default='mp4,avi,mov,mkv')
            supported_formats = [fmt.strip() for fmt in supported_formats_str.split(',')]

            # FFprobe can return multiple formats separated by commas (e.g., "mov,mp4,m4a,3gp,3g2,mj2")
            # Check if any of the detected formats is in our supported list
            detected_formats = [fmt.strip() for fmt in info["format_name"].split(',')]

            format_supported = any(fmt in supported_formats for fmt in detected_formats)
            if not format_supported:
                raise ValueError(f"Unsupported format: {info['format_name']} (supported: {', '.join(supported_formats)})")
            
            logger.info("Input file validation passed", extra={
                "task_id": task_id,
                "input_path": input_path,
                "duration": info["duration"],
                "resolution": f"{info['video']['width']}x{info['video']['height']}"
            })
            
            return True

        except Exception as e:
            logger.error("Input file validation failed", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise

    def create_custom_mpd(self, output_dir: str, base_name: str, video_info: Dict[str, Any],
                         bitrates_used: List[Dict[str, Any]], task_id: str = None) -> str:
        """
        Create a custom MPD file similar to AWS MediaConvert structure.

        Args:
            output_dir (str): Output directory
            base_name (str): Base name for files
            video_info (dict): Video information
            bitrates_used (list): List of bitrate configurations used
            task_id (str, optional): Task ID for logging

        Returns:
            str: Path to created MPD file
        """
        from xml.etree.ElementTree import Element, SubElement, tostring
        from xml.dom import minidom

        duration = video_info["duration"]
        frame_rate = video_info["video"]["fps"]

        # Convert duration to ISO 8601 format
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = duration % 60
        duration_iso = f"PT{hours}H{minutes}M{seconds:.3f}S" if hours > 0 else f"PT{minutes}M{seconds:.3f}S"

        # Create MPD root element
        mpd = Element("MPD")
        mpd.set("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance")
        mpd.set("xmlns", "urn:mpeg:dash:schema:mpd:2011")
        mpd.set("xmlns:cenc", "urn:mpeg:cenc:2013")
        mpd.set("xsi:schemaLocation", "urn:mpeg:dash:schema:mpd:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd")
        mpd.set("type", "static")
        mpd.set("minBufferTime", "PT6S")
        mpd.set("profiles", "urn:mpeg:dash:profile:isoff-main:2011")
        mpd.set("mediaPresentationDuration", duration_iso)

        # Create Period
        period = SubElement(mpd, "Period")
        period.set("start", "PT0S")
        period.set("duration", duration_iso)
        period.set("id", "1")

        # Video AdaptationSet
        video_adaptation_set = SubElement(period, "AdaptationSet")
        video_adaptation_set.set("mimeType", "video/mp4")
        video_adaptation_set.set("frameRate", f"{int(frame_rate)}/1")
        video_adaptation_set.set("segmentAlignment", "true")
        video_adaptation_set.set("subsegmentAlignment", "true")
        video_adaptation_set.set("startWithSAP", "1")
        video_adaptation_set.set("subsegmentStartsWithSAP", "1")
        video_adaptation_set.set("bitstreamSwitching", "false")

        # Video SegmentTemplate (common for all representations)
        video_segment_template = SubElement(video_adaptation_set, "SegmentTemplate")
        video_segment_template.set("timescale", "90000")
        video_segment_template.set("duration", "540000")  # 6 seconds at 90000 timescale
        video_segment_template.set("startNumber", "1")

        # Video Representations
        for bitrate_config in bitrates_used:
            width, height = bitrate_config["resolution"].split('x')
            bandwidth = int(bitrate_config["video_bitrate"].rstrip('k')) * 1000

            representation = SubElement(video_adaptation_set, "Representation")
            representation.set("id", bitrate_config["id"])
            representation.set("width", width)
            representation.set("height", height)
            representation.set("bandwidth", str(bandwidth))
            representation.set("codecs", "avc1.640028")  # H.264 High Profile

            # Individual SegmentTemplate for this representation
            segment_template = SubElement(representation, "SegmentTemplate")
            segment_template.set("media", f"{base_name}_{bitrate_config['id']}_$Number%09d$.m4s")
            segment_template.set("initialization", f"{base_name}_{bitrate_config['id']}init.m4s")
            segment_template.set("duration", "540000")
            segment_template.set("startNumber", "1")

        # Audio AdaptationSet (if audio exists)
        if video_info["audio"]:
            audio_adaptation_set = SubElement(period, "AdaptationSet")
            audio_adaptation_set.set("mimeType", "audio/mp4")
            audio_adaptation_set.set("lang", "und")
            audio_adaptation_set.set("segmentAlignment", "false")

            # Audio SegmentTemplate
            audio_segment_template = SubElement(audio_adaptation_set, "SegmentTemplate")
            audio_segment_template.set("timescale", "48000")
            audio_segment_template.set("media", f"{base_name}_{len(bitrates_used) + 1}_$Number%09d$.m4s")
            audio_segment_template.set("initialization", f"{base_name}_{len(bitrates_used) + 1}init.m4s")
            audio_segment_template.set("duration", "288000")  # 6 seconds at 48000 timescale
            audio_segment_template.set("startNumber", "1")

            # Audio Representation
            audio_bandwidth = int(bitrates_used[0]["audio_bitrate"].rstrip('k')) * 1000
            audio_representation = SubElement(audio_adaptation_set, "Representation")
            audio_representation.set("id", str(len(bitrates_used) + 1))
            audio_representation.set("bandwidth", str(audio_bandwidth))
            audio_representation.set("audioSamplingRate", "48000")
            audio_representation.set("codecs", "mp4a.40.2")

            # Audio Channel Configuration
            audio_channel_config = SubElement(audio_representation, "AudioChannelConfiguration")
            audio_channel_config.set("schemeIdUri", "urn:mpeg:dash:23003:3:audio_channel_configuration:2011")
            audio_channel_config.set("value", str(video_info["audio"]["channels"]))

        # Write MPD file
        mpd_path = os.path.join(output_dir, "manifest.mpd")

        # Pretty print XML
        rough_string = tostring(mpd, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")

        # Remove empty lines and fix encoding
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        pretty_xml = '\n'.join(lines)

        with open(mpd_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)

        logger.info("Created custom MPD file", extra={
            "task_id": task_id,
            "mpd_path": mpd_path,
            "video_representations": len(bitrates_used),
            "audio_representations": 1 if video_info["audio"] else 0
        })

        return mpd_path
