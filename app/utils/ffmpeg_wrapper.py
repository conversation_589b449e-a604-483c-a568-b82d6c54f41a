"""
FFmpeg Wrapper for Media Convert Service

This module provides a wrapper for FFmpeg to convert MP4 videos to MPEG-DASH format
with multiple bitrates for Adaptive Bitrate Streaming (ABR).
"""

import os
import subprocess
import json
import tempfile
import psutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from decouple import config
from app.utils.logger import get_logger

logger = get_logger(__name__)

class FFmpegWrapper:
    """Wrapper for FFmpeg video conversion operations."""
    
    def __init__(self):
        """Initialize FFmpeg wrapper with configuration."""
        self.ffmpeg_path = config('FFMPEG_PATH', default='ffmpeg')
        self.ffprobe_path = config('FFMPEG_PROBE_PATH', default='ffprobe')
        
        # Verify FFmpeg installation
        self._verify_ffmpeg()
        
        logger.info("FFmpeg wrapper initialized", extra={
            "ffmpeg_path": self.ffmpeg_path,
            "ffprobe_path": self.ffprobe_path
        })
    
    def _verify_ffmpeg(self):
        """Verify that <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> are available."""
        try:
            # Check FFmpeg
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg not working: {result.stderr}")
            
            # Check FFprobe
            result = subprocess.run([self.ffprobe_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe not working: {result.stderr}")
                
            logger.info("FFmpeg verification successful")
            
        except FileNotFoundError as e:
            raise RuntimeError(f"FFmpeg not found. Please install FFmpeg: {e}")
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg verification timed out")
    
    def get_video_info(self, input_path: str, task_id: str = None) -> Dict[str, Any]:
        """
        Get video information using FFprobe.
        
        Args:
            input_path (str): Path to input video file
            task_id (str, optional): Task ID for logging
            
        Returns:
            dict: Video information including duration, resolution, bitrate, etc.
        """
        logger.info("Getting video info", extra={
            "task_id": task_id,
            "input_path": input_path
        })
        
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe failed: {result.stderr}")
            
            probe_data = json.loads(result.stdout)
            
            # Extract video stream info
            video_stream = None
            audio_stream = None
            
            for stream in probe_data.get('streams', []):
                if stream.get('codec_type') == 'video' and video_stream is None:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and audio_stream is None:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found in input file")
            
            # Extract relevant information
            info = {
                "duration": float(probe_data.get('format', {}).get('duration', 0)),
                "size": int(probe_data.get('format', {}).get('size', 0)),
                "bitrate": int(probe_data.get('format', {}).get('bit_rate', 0)),
                "format_name": probe_data.get('format', {}).get('format_name', ''),
                "video": {
                    "codec": video_stream.get('codec_name', ''),
                    "width": int(video_stream.get('width', 0)),
                    "height": int(video_stream.get('height', 0)),
                    "fps": eval(video_stream.get('r_frame_rate', '0/1')),
                    "bitrate": int(video_stream.get('bit_rate', 0)) if video_stream.get('bit_rate') else None
                },
                "audio": {
                    "codec": audio_stream.get('codec_name', '') if audio_stream else None,
                    "channels": int(audio_stream.get('channels', 0)) if audio_stream else 0,
                    "sample_rate": int(audio_stream.get('sample_rate', 0)) if audio_stream else 0,
                    "bitrate": int(audio_stream.get('bit_rate', 0)) if audio_stream and audio_stream.get('bit_rate') else None
                } if audio_stream else None
            }
            
            logger.info("Video info extracted", extra={
                "task_id": task_id,
                "info": info
            })
            
            return info
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFprobe timed out")
        except json.JSONDecodeError as e:
            raise RuntimeError(f"Failed to parse FFprobe output: {e}")
        except Exception as e:
            logger.error("Failed to get video info", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise
    
    def convert_to_dash(self, input_path: str, output_dir: str, task_id: str = None,
                       bitrates: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Convert MP4 video to MPEG-DASH format with multiple bitrates.
        
        Args:
            input_path (str): Path to input MP4 file
            output_dir (str): Directory to save DASH files
            task_id (str, optional): Task ID for logging
            bitrates (list, optional): List of bitrate configurations
            
        Returns:
            dict: Conversion result with output files and metadata
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Default bitrate configurations optimized for LMS/Educational content
        if bitrates is None:
            bitrates = [
                {"resolution": "1280x720", "video_bitrate": "2500k", "audio_bitrate": "96k", "name": "720p", "id": "1"},
                {"resolution": "854x480", "video_bitrate": "1000k", "audio_bitrate": "96k", "name": "480p", "id": "2"},
                {"resolution": "640x360", "video_bitrate": "600k", "audio_bitrate": "96k", "name": "360p", "id": "3"},
                {"resolution": "426x240", "video_bitrate": "300k", "audio_bitrate": "96k", "name": "240p", "id": "4"}
            ]
        
        logger.info("Starting DASH conversion", extra={
            "task_id": task_id,
            "input_path": input_path,
            "output_dir": output_dir,
            "bitrates": bitrates
        })
        
        try:
            # Get input video info
            video_info = self.get_video_info(input_path, task_id)

            # Log system resources for EKS monitoring
            logger.info("System resources for DASH conversion", extra={
                "task_id": task_id,
                "video_duration": video_info["duration"],
                "video_resolution": f"{video_info['video']['width']}x{video_info['video']['height']}",
                "system_cpu_count": os.cpu_count(),
                "system_memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "available_memory_gb": round(psutil.virtual_memory().available / (1024**3), 2)
            })
            
            # Filter bitrates based on input resolution (no upscaling)
            input_height = video_info["video"]["height"]
            input_width = video_info["video"]["width"]

            # Only include resolutions that are same size or smaller (no upscaling)
            suitable_bitrates = []
            for br in bitrates:
                target_width, target_height = br["resolution"].split('x')
                target_h = int(target_height)
                target_w = int(target_width)

                if target_h <= input_height and target_w <= input_width:
                    suitable_bitrates.append(br)
                else:
                    logger.info(f"Skipping {br['name']} - would require upscaling", extra={
                        "task_id": task_id,
                        "target_resolution": br["resolution"],
                        "input_resolution": f"{input_width}x{input_height}"
                    })

            # If no suitable bitrates, use the smallest one as fallback
            filtered_bitrates = suitable_bitrates if suitable_bitrates else [bitrates[-1]]

            logger.info("Filtered bitrates for LMS optimization", extra={
                "task_id": task_id,
                "input_resolution": f"{input_width}x{input_height}",
                "filtered_bitrates": [f"{br['name']} ({br['resolution']})" for br in filtered_bitrates]
            })
            
            # Generate DASH with proper segmentation using single FFmpeg command
            # This approach creates proper .m4s segments and init files

            # Extract base filename for consistent naming
            base_name = Path(input_path).stem
            manifest_path = os.path.join(output_dir, "manifest.mpd")

            # Build comprehensive FFmpeg command optimized for EKS environment
            ffmpeg_threads = config('FFMPEG_THREADS', default='2')
            ffmpeg_preset = config('FFMPEG_PRESET', default='medium')

            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-y',  # Overwrite output files
                '-threads', ffmpeg_threads,  # Configurable thread count
                '-preset', ffmpeg_preset,  # Configurable preset for CPU/quality balance
                '-tune', 'zerolatency'  # Optimize for streaming
            ]

            # Use all available filtered bitrates for full multi-bitrate DASH
            selected_bitrates = filtered_bitrates

            logger.info(f"Using {len(selected_bitrates)} video bitrates for adaptive streaming", extra={
                "task_id": task_id,
                "selected_bitrates": [f"{br['name']} ({br['resolution']}) @ {br['video_bitrate']}" for br in selected_bitrates]
            })

            # Add video streams for each selected bitrate
            for i, bitrate_config in enumerate(selected_bitrates):
                cmd.extend([
                    '-map', '0:v:0',  # Map video stream
                    f'-c:v:{i}', 'libx264',
                    f'-b:v:{i}', bitrate_config["video_bitrate"],
                    f'-s:v:{i}', bitrate_config["resolution"],
                    f'-profile:v:{i}', 'high',
                    f'-level:v:{i}', '4.0',
                    f'-g:v:{i}', '72',  # GOP size
                    f'-keyint_min:v:{i}', '72',
                    f'-sc_threshold:v:{i}', '0',  # Disable scene cut detection
                    f'-force_key_frames:v:{i}', 'expr:gte(t,n_forced*6)',  # Force keyframes every 6 seconds
                ])

                logger.info(f"Adding {bitrate_config['name']} video stream", extra={
                    "task_id": task_id,
                    "stream_index": i,
                    "resolution": bitrate_config["resolution"],
                    "bitrate": bitrate_config["video_bitrate"]
                })

            # Add audio stream (if audio exists)
            if video_info["audio"]:
                cmd.extend([
                    '-map', '0:a:0',  # Map audio stream
                    '-c:a', 'aac',
                    '-b:a', selected_bitrates[0]["audio_bitrate"],  # Use first bitrate for audio
                    '-ac', '2' if video_info["audio"]["channels"] > 1 else '1',
                    '-ar', '48000',  # Standard sample rate for DASH
                ])

                logger.info("Adding audio stream", extra={
                    "task_id": task_id,
                    "bitrate": selected_bitrates[0]["audio_bitrate"]
                })

            # DASH-specific options for proper segmentation
            # Create separate adaptation sets for each video stream to avoid aspect ratio conflicts
            video_adaptation_sets = []
            for i in range(len(selected_bitrates)):
                video_adaptation_sets.append(f"id={i},streams={i}")

            # Add audio adaptation set
            audio_adaptation_set = f"id={len(selected_bitrates)},streams=a" if video_info["audio"] else ""

            # Combine all adaptation sets
            if audio_adaptation_set:
                adaptation_sets_str = " ".join(video_adaptation_sets) + " " + audio_adaptation_set
            else:
                adaptation_sets_str = " ".join(video_adaptation_sets)

            cmd.extend([
                '-f', 'dash',
                '-seg_duration', '6',  # 6-second segments (like AWS MediaConvert)
                '-use_template', '1',
                '-use_timeline', '0',  # Use number-based segments
                '-adaptation_sets', adaptation_sets_str,
                '-init_seg_name', f'{base_name}_$RepresentationID$init.m4s',
                '-media_seg_name', f'{base_name}_$RepresentationID$_$Number%09d$.m4s',
                '-single_file', '0',  # Generate separate segment files
                '-streaming', '1',  # Optimize for streaming
                'manifest.mpd'  # Use FFmpeg-generated manifest
            ])

            logger.info("DASH adaptation sets configured", extra={
                "task_id": task_id,
                "adaptation_sets": adaptation_sets_str,
                "video_streams": len(selected_bitrates),
                "audio_streams": 1 if video_info["audio"] else 0
            })

            logger.info("Creating DASH manifest", extra={
                "task_id": task_id,
                "command": " ".join(cmd),
                "output_dir": output_dir,
                "expected_segments": int(video_info["duration"] / 6) + 1  # 6-second segments
            })

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800, cwd=output_dir)

            # Always log the FFmpeg output for debugging
            logger.info("FFmpeg DASH execution completed", extra={
                "task_id": task_id,
                "return_code": result.returncode,
                "stdout_length": len(result.stdout),
                "stderr_length": len(result.stderr)
            })

            if result.returncode != 0:
                # If FFmpeg DASH creation fails, log error and raise
                logger.error("FFmpeg DASH creation failed", extra={
                    "task_id": task_id,
                    "ffmpeg_command": " ".join(cmd),
                    "ffmpeg_stdout": result.stdout,
                    "ffmpeg_stderr": result.stderr,
                    "return_code": result.returncode
                })
                raise RuntimeError(f"FFmpeg DASH creation failed with return code {result.returncode}")

            # FFmpeg succeeded - verify manifest was created
            logger.info("FFmpeg DASH creation succeeded", extra={
                "task_id": task_id,
                "output_dir": output_dir,
                "manifest_path": manifest_path
            })

            # Verify output files
            if not os.path.exists(manifest_path):
                raise RuntimeError("DASH manifest file was not created")
            
            # Collect output files
            output_files = []
            total_size = 0
            
            for file_path in Path(output_dir).rglob('*'):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    output_files.append({
                        "path": str(file_path),
                        "name": file_path.name,
                        "size": file_size,
                        "relative_path": str(file_path.relative_to(output_dir))
                    })
                    total_size += file_size
            
            result = {
                "status": "success",
                "input_path": input_path,
                "output_dir": output_dir,
                "manifest_path": manifest_path,
                "bitrates_used": filtered_bitrates,
                "output_files": output_files,
                "total_files": len(output_files),
                "total_size": total_size,
                "input_info": video_info,
                "ffmpeg_stdout": result.stdout,
                "ffmpeg_stderr": result.stderr
            }
            
            logger.info("DASH conversion completed", extra={
                "task_id": task_id,
                "result": {k: v for k, v in result.items() if k not in ["ffmpeg_stdout", "ffmpeg_stderr"]}
            })
            
            return result
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg conversion timed out")
        except Exception as e:
            logger.error("DASH conversion failed", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise
    
    def validate_input_file(self, input_path: str, task_id: str = None) -> bool:
        """
        Validate input video file.
        
        Args:
            input_path (str): Path to input file
            task_id (str, optional): Task ID for logging
            
        Returns:
            bool: True if file is valid
            
        Raises:
            ValueError: If file is invalid
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        try:
            info = self.get_video_info(input_path, task_id)
            
            # Basic validation
            if info["duration"] <= 0:
                raise ValueError("Video has no duration")
            
            if info["video"]["width"] <= 0 or info["video"]["height"] <= 0:
                raise ValueError("Invalid video resolution")
            
            # Check maximum duration (2 hours)
            try:
                max_duration_str = config('MAX_VIDEO_DURATION', default='7200')
                # Remove any comments and whitespace
                max_duration_str = max_duration_str.split('#')[0].strip()
                max_duration = int(max_duration_str)
            except (ValueError, TypeError):
                max_duration = 7200  # Default to 2 hours
                logger.warning("Invalid MAX_VIDEO_DURATION config, using default", extra={
                    "task_id": task_id,
                    "default_duration": max_duration
                })

            if info["duration"] > max_duration:
                raise ValueError(f"Video too long: {info['duration']}s (max: {max_duration}s)")
            
            # Check supported formats
            supported_formats_str = config('SUPPORTED_INPUT_FORMATS', default='mp4,avi,mov,mkv')
            supported_formats = [fmt.strip() for fmt in supported_formats_str.split(',')]

            # FFprobe can return multiple formats separated by commas (e.g., "mov,mp4,m4a,3gp,3g2,mj2")
            # Check if any of the detected formats is in our supported list
            detected_formats = [fmt.strip() for fmt in info["format_name"].split(',')]

            format_supported = any(fmt in supported_formats for fmt in detected_formats)
            if not format_supported:
                raise ValueError(f"Unsupported format: {info['format_name']} (supported: {', '.join(supported_formats)})")
            
            logger.info("Input file validation passed", extra={
                "task_id": task_id,
                "input_path": input_path,
                "duration": info["duration"],
                "resolution": f"{info['video']['width']}x{info['video']['height']}"
            })
            
            return True

        except Exception as e:
            logger.error("Input file validation failed", extra={
                "task_id": task_id,
                "error": str(e),
                "input_path": input_path
            })
            raise

    def generate_thumbnails(self, input_path: str, output_dir: str, video_info: Dict[str, Any], task_id: str = None) -> Dict[str, Any]:
        """
        Generate thumbnails for video: poster, timeline thumbnails, and preview grid.

        Args:
            input_path (str): Path to input video file
            output_dir (str): Directory to save thumbnails
            video_info (dict): Video information from get_video_info
            task_id (str, optional): Task ID for logging

        Returns:
            dict: Generated thumbnail information
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")

        os.makedirs(output_dir, exist_ok=True)

        duration = video_info["duration"]
        width = video_info["video"]["width"]
        height = video_info["video"]["height"]

        # Calculate thumbnail dimensions (maintain aspect ratio, max 320px width)
        thumb_width = min(320, width)
        thumb_height = int((thumb_width / width) * height)

        logger.info("Starting thumbnail generation", extra={
            "task_id": task_id,
            "input_path": input_path,
            "output_dir": output_dir,
            "video_duration": duration,
            "original_resolution": f"{width}x{height}",
            "thumbnail_resolution": f"{thumb_width}x{thumb_height}"
        })

        thumbnails_generated = {}

        try:
            # 1. Generate poster thumbnail (at 10% of video duration)
            poster_result = self._generate_poster_thumbnail(
                input_path, output_dir, duration, thumb_width, thumb_height, task_id
            )
            thumbnails_generated["poster"] = poster_result

            # 2. Generate timeline thumbnails (every 10 seconds)
            timeline_result = self._generate_timeline_thumbnails(
                input_path, output_dir, duration, thumb_width, thumb_height, task_id
            )
            thumbnails_generated["timeline"] = timeline_result

            # 3. Generate preview grid (9 frames in a 3x3 grid)
            grid_result = self._generate_preview_grid(
                input_path, output_dir, duration, thumb_width, thumb_height, task_id
            )
            thumbnails_generated["preview_grid"] = grid_result

            logger.info("Thumbnail generation completed", extra={
                "task_id": task_id,
                "thumbnails_generated": thumbnails_generated,
                "total_files": sum(len(result.get("files", [])) for result in thumbnails_generated.values())
            })

            return {
                "status": "success",
                "thumbnails": thumbnails_generated,
                "output_dir": output_dir
            }

        except Exception as e:
            logger.error("Thumbnail generation failed", extra={
                "task_id": task_id,
                "input_path": input_path,
                "output_dir": output_dir,
                "error": str(e)
            })
            raise

    def _generate_poster_thumbnail(self, input_path: str, output_dir: str, duration: float,
                                 width: int, height: int, task_id: str = None) -> Dict[str, Any]:
        """Generate main poster thumbnail at 10% of video duration."""
        poster_time = duration * 0.1  # 10% into the video
        poster_path = os.path.join(output_dir, "poster.jpg")

        cmd = [
            self.ffmpeg_path,
            '-i', input_path,
            '-ss', str(poster_time),  # Seek to specific time
            '-vframes', '1',  # Extract only 1 frame
            '-vf', f'scale={width}:{height}',  # Resize
            '-q:v', '2',  # High quality JPEG
            '-y',  # Overwrite
            poster_path
        ]

        logger.debug("Generating poster thumbnail", extra={
            "task_id": task_id,
            "command": " ".join(cmd),
            "poster_time": poster_time
        })

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            raise RuntimeError(f"Poster thumbnail generation failed: {result.stderr}")

        return {
            "type": "poster",
            "files": [poster_path],
            "timestamp": poster_time,
            "resolution": f"{width}x{height}"
        }

    def _generate_timeline_thumbnails(self, input_path: str, output_dir: str, duration: float,
                                    width: int, height: int, task_id: str = None) -> Dict[str, Any]:
        """Generate timeline thumbnails every 10 seconds for video scrubbing."""
        timeline_dir = os.path.join(output_dir, "timeline")
        os.makedirs(timeline_dir, exist_ok=True)

        # Generate thumbnail every 10 seconds
        interval = 10.0
        thumbnail_times = []
        current_time = 0.0

        while current_time < duration:
            thumbnail_times.append(current_time)
            current_time += interval

        # Add final frame if not already included
        if thumbnail_times[-1] < duration - 1:
            thumbnail_times.append(duration - 1)

        generated_files = []

        for i, timestamp in enumerate(thumbnail_times):
            thumb_path = os.path.join(timeline_dir, f"thumb_{i:04d}.jpg")

            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-ss', str(timestamp),
                '-vframes', '1',
                '-vf', f'scale={width}:{height}',
                '-q:v', '3',  # Good quality for timeline
                '-y',
                thumb_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                generated_files.append({
                    "path": thumb_path,
                    "timestamp": timestamp,
                    "index": i
                })

        logger.info("Generated timeline thumbnails", extra={
            "task_id": task_id,
            "count": len(generated_files),
            "interval": interval,
            "duration": duration
        })

        return {
            "type": "timeline",
            "files": generated_files,
            "interval": interval,
            "count": len(generated_files),
            "resolution": f"{width}x{height}"
        }

    def _generate_preview_grid(self, input_path: str, output_dir: str, duration: float,
                             width: int, height: int, task_id: str = None) -> Dict[str, Any]:
        """Generate a 3x3 grid preview image with 9 frames from the video."""
        grid_path = os.path.join(output_dir, "preview_grid.jpg")

        # Calculate timestamps for 9 frames evenly distributed
        grid_size = 3  # 3x3 grid
        total_frames = grid_size * grid_size

        # Skip first and last 5% to avoid black frames or credits
        start_time = duration * 0.05
        end_time = duration * 0.95
        effective_duration = end_time - start_time

        # Calculate frame timestamps
        frame_interval = effective_duration / (total_frames - 1)
        timestamps = [start_time + (i * frame_interval) for i in range(total_frames)]

        # Individual thumbnail size for grid (smaller than timeline thumbs)
        grid_thumb_width = width // 2  # Half size for grid
        grid_thumb_height = height // 2

        # Use FFmpeg's tile filter to create grid
        # First, we need to extract frames at specific timestamps
        temp_dir = os.path.join(output_dir, "temp_grid")
        os.makedirs(temp_dir, exist_ok=True)

        try:
            # Generate individual frames for grid
            frame_files = []
            for i, timestamp in enumerate(timestamps):
                frame_path = os.path.join(temp_dir, f"frame_{i:02d}.jpg")

                cmd = [
                    self.ffmpeg_path,
                    '-i', input_path,
                    '-ss', str(timestamp),
                    '-vframes', '1',
                    '-vf', f'scale={grid_thumb_width}:{grid_thumb_height}',
                    '-q:v', '3',
                    '-y',
                    frame_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    frame_files.append(frame_path)

            if len(frame_files) >= 9:
                # Create grid using FFmpeg's tile filter
                # Input all 9 frames and tile them in 3x3 grid
                cmd = [
                    self.ffmpeg_path,
                    '-i', frame_files[0], '-i', frame_files[1], '-i', frame_files[2],
                    '-i', frame_files[3], '-i', frame_files[4], '-i', frame_files[5],
                    '-i', frame_files[6], '-i', frame_files[7], '-i', frame_files[8],
                    '-filter_complex',
                    '[0:v][1:v][2:v]hstack=inputs=3[top];'
                    '[3:v][4:v][5:v]hstack=inputs=3[middle];'
                    '[6:v][7:v][8:v]hstack=inputs=3[bottom];'
                    '[top][middle][bottom]vstack=inputs=3[grid]',
                    '-map', '[grid]',
                    '-q:v', '2',  # High quality for preview
                    '-y',
                    grid_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    raise RuntimeError(f"Grid generation failed: {result.stderr}")

            # Clean up temporary files
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)

            logger.info("Generated preview grid", extra={
                "task_id": task_id,
                "grid_path": grid_path,
                "frames_used": len(frame_files),
                "grid_size": f"{grid_size}x{grid_size}",
                "final_resolution": f"{grid_thumb_width * grid_size}x{grid_thumb_height * grid_size}"
            })

            return {
                "type": "preview_grid",
                "files": [grid_path],
                "grid_size": f"{grid_size}x{grid_size}",
                "frames_count": len(frame_files),
                "timestamps": timestamps[:len(frame_files)],
                "resolution": f"{grid_thumb_width * grid_size}x{grid_thumb_height * grid_size}"
            }

        except Exception as e:
            # Clean up on error
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            raise e
