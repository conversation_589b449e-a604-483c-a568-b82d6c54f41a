"""
Video conversion tasks for Media Convert Service
"""

import os
import tempfile
import shutil
from celery import current_task
from app.celery_app import celery_app
from app.utils.logger import get_logger, log_video_conversion_metrics
from app.utils.s3_client import S3Client
from app.utils.ffmpeg_wrapper import FFmpegWrapper
from app.tasks.notification import send_completion_notification, send_error_notification
from decouple import config

logger = get_logger(__name__)

@celery_app.task(bind=True)
def convert_video_to_dash(self, input_s3_url, output_s3_prefix, webhook_url=None, bitrates=None):
    """
    Convert MP4 video to MPEG-DASH format.

    Args:
        input_s3_url (str): S3 URL of the input MP4 file
        output_s3_prefix (str): S3 prefix for output DASH files
        webhook_url (str, optional): Webhook URL for notifications
        bitrates (list, optional): Custom bitrate configurations

    Returns:
        dict: Conversion result with status and output URLs
    """
    task_id = self.request.id
    temp_dir = None

    logger.info("Video conversion task started", extra={
        "task_id": task_id,
        "input_s3_url": input_s3_url,
        "output_s3_prefix": output_s3_prefix,
        "webhook_url": webhook_url
    })

    try:
        # Initialize clients
        s3_client = S3Client()
        ffmpeg = FFmpegWrapper()

        # Create temporary directory for processing
        temp_dir = tempfile.mkdtemp(prefix=f"media_convert_{task_id}_")
        input_file = os.path.join(temp_dir, "input.mp4")
        output_dir = os.path.join(temp_dir, "output")

        logger.info("Created temporary directory", extra={
            "task_id": task_id,
            "temp_dir": temp_dir
        })

        # Step 1: Download video from S3
        logger.info("Downloading video from S3", extra={"task_id": task_id})
        download_result = s3_client.download_file(input_s3_url, input_file, task_id)

        # Step 2: Validate input file
        logger.info("Validating input file", extra={"task_id": task_id})
        ffmpeg.validate_input_file(input_file, task_id)

        # Step 3: Convert to DASH (thumbnails generated automatically)
        logger.info("Converting video to DASH with thumbnails", extra={"task_id": task_id})
        conversion_result = ffmpeg.convert_to_dash(input_file, output_dir, task_id, bitrates)

        # Step 4: Upload DASH files and thumbnails to S3
        logger.info("Uploading DASH files to S3", extra={"task_id": task_id})
        upload_result = s3_client.upload_directory(output_dir, output_s3_prefix, task_id)

        # Prepare final result
        result = {
            "status": "success",
            "task_id": task_id,
            "input_url": input_s3_url,
            "output_prefix": output_s3_prefix,
            "dash_manifest_url": f"{output_s3_prefix}/manifest.mpd",
            "download_info": download_result,
            "conversion_info": {
                "input_info": conversion_result["input_info"],
                "bitrates_used": conversion_result["bitrates_used"],
                "total_files": conversion_result["total_files"],
                "total_size": conversion_result["total_size"]
            },
            "upload_info": upload_result,
            "message": "Video conversion completed successfully"
        }

        # Log conversion metrics
        log_video_conversion_metrics(
            task_id=task_id,
            input_file_size=download_result["file_size"],
            output_file_size=upload_result["total_size"],
            duration=conversion_result["input_info"]["duration"],
            bitrates=[br["video_bitrate"] for br in conversion_result["bitrates_used"]],
            resolution=f"{conversion_result['input_info']['video']['width']}x{conversion_result['input_info']['video']['height']}"
        )

        logger.info("Video conversion completed", extra={
            "task_id": task_id,
            "result": {k: v for k, v in result.items() if k not in ["download_info", "upload_info"]}
        })

        # Send success notification
        if webhook_url:
            send_completion_notification.delay(result, webhook_url)

        return result

    except Exception as e:
        error_info = {
            "task_id": task_id,
            "error": str(e),
            "input_url": input_s3_url,
            "output_prefix": output_s3_prefix
        }

        logger.error("Video conversion failed", extra=error_info)

        # Send error notification
        if webhook_url:
            send_error_notification.delay(error_info, webhook_url)

        raise

    finally:
        # Cleanup temporary directory
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info("Cleaned up temporary directory", extra={
                    "task_id": task_id,
                    "temp_dir": temp_dir
                })
            except Exception as e:
                logger.warning("Failed to cleanup temporary directory", extra={
                    "task_id": task_id,
                    "temp_dir": temp_dir,
                    "error": str(e)
                })
