"""
Test tasks for verifying Celery functionality
"""

from celery import current_task
from app.celery_app import celery_app
from app.utils.logger import get_logger
import time

logger = get_logger(__name__)

@celery_app.task(bind=True)
def test_basic_task(self):
    """Basic test task to verify Celery is working."""
    logger.info("Test task started", extra={"task_id": self.request.id})
    
    # Simulate some work
    time.sleep(2)
    
    result = {
        "status": "success",
        "task_id": self.request.id,
        "message": "Basic test task completed successfully"
    }
    
    logger.info("Test task completed", extra={"task_id": self.request.id, "result": result})
    return result

@celery_app.task(bind=True)
def test_retry_task(self, fail_count=0):
    """Test task that demonstrates retry functionality."""
    logger.info("Retry test task started", extra={
        "task_id": self.request.id,
        "fail_count": fail_count,
        "retry_count": self.request.retries
    })
    
    # Fail the first few attempts to test retry
    if self.request.retries < fail_count:
        logger.warning("Task intentionally failing for retry test", extra={
            "task_id": self.request.id,
            "retry_count": self.request.retries,
            "target_fail_count": fail_count
        })
        raise Exception(f"Intentional failure for retry test (attempt {self.request.retries + 1})")
    
    result = {
        "status": "success",
        "task_id": self.request.id,
        "message": f"Retry test completed after {self.request.retries} retries",
        "total_retries": self.request.retries
    }
    
    logger.info("Retry test task completed", extra={"task_id": self.request.id, "result": result})
    return result

@celery_app.task(bind=True)
def test_queue_routing(self):
    """Test task to verify queue routing is working."""
    logger.info("Queue routing test started", extra={
        "task_id": self.request.id,
        "queue": getattr(self.request, 'delivery_info', {}).get('routing_key', 'unknown')
    })
    
    result = {
        "status": "success",
        "task_id": self.request.id,
        "message": "Queue routing test completed",
        "queue_info": getattr(self.request, 'delivery_info', {})
    }
    
    logger.info("Queue routing test completed", extra={"task_id": self.request.id, "result": result})
    return result
