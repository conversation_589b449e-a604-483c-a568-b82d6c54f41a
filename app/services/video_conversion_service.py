"""Video Conversion Service for database operations"""

from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy.exc import SQLAlchemyError

from app.database.connection import get_db_session
from app.database.models import VideoConversion
from app.utils.logger import get_logger

logger = get_logger(__name__)


class VideoConversionService:
    """Service for managing video conversion records in the database"""

    @staticmethod
    def create_conversion(
        job_id: str, input_path: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[VideoConversion]:
        """
        Create a new video conversion record

        Args:
            job_id: Unique job identifier (usually Celery task ID)
            input_path: Path to the input video file
            metadata: Optional metadata dictionary

        Returns:
            VideoConversion object or None if failed
        """
        try:
            with next(get_db_session()) as session:
                conversion = VideoConversion(
                    job_id=job_id,
                    input_path=input_path,
                    status="PENDING",
                    metadata_json=metadata or {},
                )

                session.add(conversion)
                session.commit()
                session.refresh(conversion)

                logger.info(
                    f"✅ Created conversion record: {conversion.id} (job: {job_id})"
                )
                return conversion

        except SQLAlchemyError as e:
            logger.error(f"❌ Failed to create conversion record for job {job_id}: {e}")
            return None

    @staticmethod
    def update_status(
        job_id: str,
        status: str,
        output_path: Optional[str] = None,
        duration_seconds: Optional[int] = None,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Update conversion status and details

        Args:
            job_id: Job identifier
            status: New status (PENDING, PROCESSING, COMPLETED, FAILED)
            output_path: Path to output file (for COMPLETED status)
            duration_seconds: Processing duration
            error_message: Error details (for FAILED status)
            metadata: Additional metadata to merge

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            with next(get_db_session()) as session:
                conversion = (
                    session.query(VideoConversion).filter_by(job_id=job_id).first()
                )

                if not conversion:
                    logger.error(f"❌ Conversion record not found for job: {job_id}")
                    return False

                # Update fields
                conversion.status = status
                conversion.updated_at = datetime.utcnow()

                if output_path:
                    conversion.output_path = output_path

                if duration_seconds is not None:
                    conversion.duration_seconds = duration_seconds

                if error_message:
                    conversion.error_message = error_message

                # Merge metadata
                if metadata:
                    current_metadata = conversion.metadata_json or {}
                    current_metadata.update(metadata)
                    conversion.metadata_json = current_metadata

                session.commit()

                logger.info(
                    f"✅ Updated conversion {conversion.id} to status: {status}"
                )
                return True

        except SQLAlchemyError as e:
            logger.error(f"❌ Failed to update conversion for job {job_id}: {e}")
            return False

    @staticmethod
    def get_conversion(job_id: str) -> Optional[VideoConversion]:
        """
        Get conversion record by job ID

        Args:
            job_id: Job identifier

        Returns:
            VideoConversion object or None if not found
        """
        try:
            with next(get_db_session()) as session:
                conversion = (
                    session.query(VideoConversion).filter_by(job_id=job_id).first()
                )

                if conversion:
                    # Detach from session to avoid lazy loading issues
                    session.expunge(conversion)

                return conversion

        except SQLAlchemyError as e:
            logger.error(f"❌ Failed to get conversion for job {job_id}: {e}")
            return None

    @staticmethod
    def get_conversion_by_id(conversion_id: str) -> Optional[VideoConversion]:
        """
        Get conversion record by UUID

        Args:
            conversion_id: Conversion UUID

        Returns:
            VideoConversion object or None if not found
        """
        try:
            with next(get_db_session()) as session:
                conversion = (
                    session.query(VideoConversion).filter_by(id=conversion_id).first()
                )

                if conversion:
                    session.expunge(conversion)

                return conversion

        except SQLAlchemyError as e:
            logger.error(f"❌ Failed to get conversion by ID {conversion_id}: {e}")
            return None

    @staticmethod
    def list_conversions(
        status: Optional[str] = None, limit: int = 100, offset: int = 0
    ) -> list[VideoConversion]:
        """
        List conversion records with optional filtering

        Args:
            status: Filter by status (optional)
            limit: Maximum number of records
            offset: Number of records to skip

        Returns:
            List of VideoConversion objects
        """
        try:
            with next(get_db_session()) as session:
                query = session.query(VideoConversion)

                if status:
                    query = query.filter_by(status=status)

                conversions = (
                    query.order_by(VideoConversion.created_at.desc())
                    .offset(offset)
                    .limit(limit)
                    .all()
                )

                # Detach from session
                for conversion in conversions:
                    session.expunge(conversion)

                return conversions

        except SQLAlchemyError as e:
            logger.error(f"❌ Failed to list conversions: {e}")
            return []
