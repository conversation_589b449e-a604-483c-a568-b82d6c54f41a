"""API functions for video conversion"""

import uuid
from typing import Dict, Any, Optional

from app.tasks.video_conversion import convert_video_to_dash
from app.services.video_conversion_service import VideoConversionService
from app.utils.logger import get_logger

logger = get_logger(__name__)


def start_video_conversion(
    input_path: str,
    webhook_url: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Start a video conversion process
    
    Args:
        input_path: Path to the input video file (S3 key or local path)
        webhook_url: Optional webhook URL for notifications
        metadata: Optional metadata dictionary
        
    Returns:
        Dictionary with job information
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        logger.info(f"🚀 Starting video conversion - Job ID: {job_id}")
        logger.info(f"📁 Input path: {input_path}")
        
        # Prepare metadata
        job_metadata = {
            "input_path": input_path,
            "webhook_url": webhook_url,
            "requested_at": str(uuid.uuid1().time),
            **(metadata or {})
        }
        
        # Create database record
        conversion = VideoConversionService.create_conversion(
            job_id=job_id,
            input_path=input_path,
            metadata=job_metadata
        )
        
        if not conversion:
            logger.error(f"❌ Failed to create database record for job: {job_id}")
            return {
                "success": False,
                "error": "Failed to create conversion record",
                "job_id": job_id
            }
        
        # Send task to Celery with the database record ID
        task = convert_video_to_dash.delay(
            input_path=input_path,
            job_id=job_id,
            conversion_id=str(conversion.id),
            webhook_url=webhook_url
        )
        
        logger.info(f"✅ Video conversion task queued - Job ID: {job_id}, Task ID: {task.id}")
        
        return {
            "success": True,
            "job_id": job_id,
            "conversion_id": str(conversion.id),
            "celery_task_id": task.id,
            "status": "PENDING",
            "message": "Video conversion started successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to start video conversion: {e}")
        return {
            "success": False,
            "error": str(e),
            "job_id": job_id if 'job_id' in locals() else None
        }


def get_conversion_status(job_id: str) -> Dict[str, Any]:
    """
    Get conversion status by job ID
    
    Args:
        job_id: Job identifier
        
    Returns:
        Dictionary with conversion status
    """
    try:
        conversion = VideoConversionService.get_conversion(job_id)
        
        if not conversion:
            return {
                "success": False,
                "error": "Conversion not found",
                "job_id": job_id
            }
        
        return {
            "success": True,
            "job_id": job_id,
            "conversion": conversion.to_dict()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get conversion status for job {job_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "job_id": job_id
        }
