#!/bin/bash

# Faz com que todas as variáveis declaradas sejam exportadas automaticamente
set -a

# Carrega o arquivo .env
source .env

# Desativa o comportamento de export automático
set +a

python -c "
from app.api.conversion_api import start_video_conversion

# Test with real S3 video
result = start_video_conversion(
    input_path='clients/e76b5082-f4fe-4f41-be79-1977840e16a8/general/teste_8d01.mp4',
    webhook_url='https://webhook.site/your-webhook-id',
    metadata={'test': True, 'source': 'local_test'}
)

print('🚀 Video Conversion Test Result:')
print(f'Success: {result[\"success\"]}')
if result['success']:
    print(f'Job ID: {result[\"job_id\"]}')
    print(f'Conversion ID: {result[\"conversion_id\"]}')
    print(f'Celery Task ID: {result[\"celery_task_id\"]}')
    print(f'Status: {result[\"status\"]}')
    print(f'Message: {result[\"message\"]}')
else:
    print(f'Error: {result[\"error\"]}')

print('\\n📋 Check logs with: docker logs media-convert-worker -f')
print('📊 Check database with: make db-test')
"