/* Video Player Styles */
.video-player-container {
  max-width: 800px;
  margin: 0 auto;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-controls {
  background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
  padding: 20px;
  position: relative;
}

.progress-container {
  position: relative;
  height: 20px;
  margin-bottom: 15px;
  cursor: pointer;
}

.progress-track {
  width: 100%;
  height: 6px;
  background: rgba(255,255,255,0.3);
  border-radius: 3px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.progress-fill {
  height: 100%;
  background: #007bff;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.thumbnail-preview {
  position: absolute;
  bottom: 25px;
  transform: translateX(-50%);
  background: #000;
  border: 2px solid #fff;
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.5);
  z-index: 10;
  pointer-events: none;
}

.thumbnail-image {
  display: block;
  width: 160px;
  height: auto;
  border-radius: 4px;
}

.thumbnail-time {
  background: rgba(0,0,0,0.9);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  border-radius: 0 0 4px 4px;
  font-family: monospace;
}

.controls-row {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
}

.play-pause-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.play-pause-btn:hover {
  background: #0056b3;
}

.time-display {
  font-family: monospace;
  font-size: 14px;
  color: rgba(255,255,255,0.9);
}

.time-separator {
  margin: 0 4px;
}

/* Video Gallery Styles */
.video-app {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px;
}

.video-player-section {
  max-width: 1000px;
  margin: 0 auto;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-btn:hover {
  background: #545b62;
}

.video-details {
  background: white;
  padding: 30px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.video-details h1 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 28px;
}

.video-details p {
  color: #666;
  line-height: 1.6;
  font-size: 16px;
}

.video-gallery {
  max-width: 1200px;
  margin: 0 auto;
}

.video-gallery h1 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
  font-size: 32px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
}

.video-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.poster-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .poster-image {
  transform: scale(1.05);
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.preview-grid-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: 6px;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.7);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.video-card:hover .play-overlay {
  background: rgba(0,0,0,0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  color: white;
  font-size: 20px;
  margin-left: 3px; /* Optical alignment */
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.video-info {
  padding: 20px;
}

.video-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.video-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-player-container {
    margin: 0 10px;
  }
  
  .thumbnail-image {
    width: 120px;
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }
  
  .video-app {
    padding: 10px;
  }
}
