// Example API responses and usage patterns for thumbnails

// 1. API Response from video conversion task
const conversionResponse = {
  "status": "success",
  "task_id": "abc123",
  "input_url": "s3://bucket/input/video.mp4",
  "output_prefix": "s3://bucket/videos/video-id",
  "dash_manifest_url": "s3://bucket/videos/video-id/manifest.mpd",
  "thumbnail_info": {
    "poster": {
      "type": "poster",
      "files": ["poster.jpg"],
      "timestamp": 38.36,
      "resolution": "320x180"
    },
    "timeline": {
      "count": 39,
      "interval": 10.0
    },
    "preview_grid": {
      "type": "preview_grid", 
      "files": ["preview_grid.jpg"],
      "grid_size": "3x3",
      "frames_count": 9
    }
  },
  "conversion_info": {
    "input_info": {
      "duration": 383.6,
      "resolution": "1280x720"
    }
  }
};

// 2. Frontend helper functions
class ThumbnailManager {
  constructor(baseUrl, thumbnailInfo) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.thumbnailInfo = thumbnailInfo;
  }

  // Get poster thumbnail URL
  getPosterUrl() {
    return `${this.baseUrl}/poster.jpg`;
  }

  // Get preview grid URL
  getPreviewGridUrl() {
    return `${this.baseUrl}/preview_grid.jpg`;
  }

  // Get timeline thumbnail URL for specific time
  getTimelineThumbnailUrl(timeInSeconds) {
    const index = Math.floor(timeInSeconds / this.thumbnailInfo.timeline.interval);
    const clampedIndex = Math.min(index, this.thumbnailInfo.timeline.count - 1);
    const paddedIndex = clampedIndex.toString().padStart(4, '0');
    return `${this.baseUrl}/timeline/thumb_${paddedIndex}.jpg`;
  }

  // Get all timeline thumbnail URLs
  getAllTimelineThumbnails() {
    const thumbnails = [];
    for (let i = 0; i < this.thumbnailInfo.timeline.count; i++) {
      const paddedIndex = i.toString().padStart(4, '0');
      thumbnails.push({
        index: i,
        timestamp: i * this.thumbnailInfo.timeline.interval,
        url: `${this.baseUrl}/timeline/thumb_${paddedIndex}.jpg`
      });
    }
    return thumbnails;
  }

  // Preload critical thumbnails
  preloadThumbnails() {
    const imagesToPreload = [
      this.getPosterUrl(),
      this.getPreviewGridUrl(),
      // Preload first few timeline thumbnails
      ...this.getAllTimelineThumbnails().slice(0, 5).map(t => t.url)
    ];

    imagesToPreload.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  }
}

// 3. React Hook for thumbnail management
function useThumbnails(videoId, thumbnailInfo) {
  const [thumbnailManager, setThumbnailManager] = useState(null);
  const [preloadedThumbnails, setPreloadedThumbnails] = useState(new Set());

  useEffect(() => {
    if (videoId && thumbnailInfo) {
      const baseUrl = `https://your-bucket.s3.amazonaws.com/videos/${videoId}`;
      const manager = new ThumbnailManager(baseUrl, thumbnailInfo);
      setThumbnailManager(manager);
      
      // Preload critical thumbnails
      manager.preloadThumbnails();
    }
  }, [videoId, thumbnailInfo]);

  const getThumbnailForTime = useCallback((timeInSeconds) => {
    if (!thumbnailManager) return null;
    return thumbnailManager.getTimelineThumbnailUrl(timeInSeconds);
  }, [thumbnailManager]);

  const preloadThumbnail = useCallback((url) => {
    if (!preloadedThumbnails.has(url)) {
      const img = new Image();
      img.onload = () => {
        setPreloadedThumbnails(prev => new Set([...prev, url]));
      };
      img.src = url;
    }
  }, [preloadedThumbnails]);

  return {
    thumbnailManager,
    getThumbnailForTime,
    preloadThumbnail,
    isPreloaded: (url) => preloadedThumbnails.has(url)
  };
}

// 4. Express.js API endpoints
const express = require('express');
const router = express.Router();

// Get video metadata including thumbnails
router.get('/api/videos/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    
    // Fetch from database
    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Build thumbnail URLs
    const baseUrl = `https://your-bucket.s3.amazonaws.com/videos/${videoId}`;
    
    const response = {
      id: video.id,
      title: video.title,
      description: video.description,
      duration: video.duration,
      dashUrl: `${baseUrl}/manifest.mpd`,
      thumbnails: {
        poster: `${baseUrl}/poster.jpg`,
        previewGrid: `${baseUrl}/preview_grid.jpg`,
        timeline: {
          baseUrl: `${baseUrl}/timeline`,
          pattern: 'thumb_{index:04d}.jpg',
          count: video.thumbnailCount,
          interval: video.thumbnailInterval
        }
      },
      createdAt: video.createdAt,
      updatedAt: video.updatedAt
    };

    res.json(response);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get thumbnail sprite sheet (for advanced implementations)
router.get('/api/videos/:videoId/sprite', async (req, res) => {
  try {
    const { videoId } = req.params;
    const video = await Video.findById(videoId);
    
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Generate sprite sheet metadata
    const spriteData = {
      url: `https://your-bucket.s3.amazonaws.com/videos/${videoId}/sprite.jpg`,
      thumbnailWidth: 160,
      thumbnailHeight: 90,
      columns: 10,
      rows: Math.ceil(video.thumbnailCount / 10),
      interval: video.thumbnailInterval,
      count: video.thumbnailCount
    };

    res.json(spriteData);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 5. Usage examples

// Basic usage
const videoData = {
  id: 'video-123',
  thumbnailInfo: conversionResponse.thumbnail_info
};

const manager = new ThumbnailManager(
  'https://your-bucket.s3.amazonaws.com/videos/video-123',
  videoData.thumbnailInfo
);

// Get poster for video card
const posterUrl = manager.getPosterUrl();

// Get thumbnail for 2 minutes 30 seconds into video
const thumbnailAt150s = manager.getTimelineThumbnailUrl(150);

// Get preview grid for hover effect
const previewGridUrl = manager.getPreviewGridUrl();

// React component usage
function VideoPlayer({ videoId }) {
  const [videoData, setVideoData] = useState(null);
  const { thumbnailManager, getThumbnailForTime } = useThumbnails(
    videoId, 
    videoData?.thumbnailInfo
  );

  useEffect(() => {
    fetch(`/api/videos/${videoId}`)
      .then(res => res.json())
      .then(setVideoData);
  }, [videoId]);

  const handleTimelineHover = (timeInSeconds) => {
    const thumbnailUrl = getThumbnailForTime(timeInSeconds);
    // Show thumbnail preview
  };

  return (
    <div>
      {videoData && (
        <>
          <img src={thumbnailManager?.getPosterUrl()} alt="Video poster" />
          {/* Video player with timeline hover */}
        </>
      )}
    </div>
  );
}

module.exports = { ThumbnailManager, useThumbnails };
