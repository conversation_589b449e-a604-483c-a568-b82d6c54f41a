<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Video Player with Auto Thumbnails</title>
    <style>
        .video-container {
            max-width: 800px;
            margin: 20px auto;
            position: relative;
        }
        
        video {
            width: 100%;
            height: auto;
        }
        
        .progress-container {
            position: relative;
            height: 20px;
            background: #555;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            border-radius: 10px;
            width: 0%;
        }
        
        .thumbnail-preview {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: #000;
            border: 2px solid #fff;
            border-radius: 4px;
            padding: 2px;
            display: none;
            z-index: 1000;
        }
        
        .thumbnail-preview img {
            width: 160px;
            height: 90px;
            object-fit: cover;
        }
        
        .thumbnail-time {
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            font-size: 12px;
            text-align: center;
        }
        
        .controls {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #333;
            color: white;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="video-container">
        <video id="videoPlayer" preload="metadata">
            <source src="https://your-bucket.s3.amazonaws.com/videos/video-id/manifest.mpd" type="application/dash+xml">
        </video>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
            <div class="thumbnail-preview" id="thumbnailPreview">
                <img id="thumbnailImg" src="" alt="Thumbnail">
                <div class="thumbnail-time" id="thumbnailTime">00:00</div>
            </div>
        </div>
        
        <div class="controls">
            <button id="playBtn">▶️ Play</button>
            <span id="timeDisplay">00:00 / 00:00</span>
        </div>
    </div>

    <script>
        class SimpleVideoPlayer {
            constructor() {
                this.video = document.getElementById('videoPlayer');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressBar = document.getElementById('progressBar');
                this.thumbnailPreview = document.getElementById('thumbnailPreview');
                this.thumbnailImg = document.getElementById('thumbnailImg');
                this.thumbnailTime = document.getElementById('thumbnailTime');
                this.playBtn = document.getElementById('playBtn');
                this.timeDisplay = document.getElementById('timeDisplay');
                
                // Configuration - no complex setup needed!
                this.baseUrl = 'https://your-bucket.s3.amazonaws.com/videos/video-id';
                this.thumbnailInterval = 10; // Thumbnails every 10 seconds
                
                this.init();
            }
            
            init() {
                this.video.addEventListener('timeupdate', () => this.updateProgress());
                this.video.addEventListener('loadedmetadata', () => this.updateTimeDisplay());
                
                this.playBtn.addEventListener('click', () => this.togglePlay());
                
                this.progressContainer.addEventListener('click', (e) => this.seek(e));
                this.progressContainer.addEventListener('mousemove', (e) => this.showThumbnail(e));
                this.progressContainer.addEventListener('mouseleave', () => this.hideThumbnail());
            }
            
            togglePlay() {
                if (this.video.paused) {
                    this.video.play();
                    this.playBtn.textContent = '⏸️ Pause';
                } else {
                    this.video.pause();
                    this.playBtn.textContent = '▶️ Play';
                }
            }
            
            updateProgress() {
                const progress = (this.video.currentTime / this.video.duration) * 100;
                this.progressBar.style.width = progress + '%';
                this.updateTimeDisplay();
            }
            
            updateTimeDisplay() {
                const current = this.formatTime(this.video.currentTime);
                const duration = this.formatTime(this.video.duration);
                this.timeDisplay.textContent = `${current} / ${duration}`;
            }
            
            seek(e) {
                const rect = this.progressContainer.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const percentage = clickX / rect.width;
                this.video.currentTime = percentage * this.video.duration;
            }
            
            showThumbnail(e) {
                const rect = this.progressContainer.getBoundingClientRect();
                const hoverX = e.clientX - rect.left;
                const percentage = hoverX / rect.width;
                const hoverTime = percentage * this.video.duration;
                
                // Simple calculation - AWS MediaConvert style
                const thumbnailIndex = Math.floor(hoverTime / this.thumbnailInterval) + 1;
                const paddedIndex = thumbnailIndex.toString().padStart(4, '0');
                
                // Simple URL pattern
                const thumbnailUrl = `${this.baseUrl}/video_thumbnail_${paddedIndex}.jpg`;
                
                this.thumbnailImg.src = thumbnailUrl;
                this.thumbnailTime.textContent = this.formatTime(hoverTime);
                
                // Position thumbnail
                const previewX = Math.max(80, Math.min(hoverX, rect.width - 80));
                this.thumbnailPreview.style.left = previewX + 'px';
                this.thumbnailPreview.style.display = 'block';
            }
            
            hideThumbnail() {
                this.thumbnailPreview.style.display = 'none';
            }
            
            formatTime(seconds) {
                if (!seconds || isNaN(seconds)) return '00:00';
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleVideoPlayer();
        });
    </script>
</body>
</html>
