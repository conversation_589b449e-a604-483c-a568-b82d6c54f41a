# Media Convert Service

Microserviço assíncrono em Python para converter vídeos MP4 para MPEG-DASH usando Celery, RabbitMQ e FFmpeg.

## 🚀 Quick Start

```bash
# Setup inicial
make setup

# Iniciar serviços essenciais
make up

# Iniciar worker
make worker

# Ou tudo de uma vez
make dev
```

## 📋 Comandos Disponíveis

```bash
make help          # Mostrar todos os comandos
make setup         # Setup inicial do projeto
make up            # Iniciar RabbitMQ + Redis
make worker        # Iniciar worker
make dev           # Ambiente completo de desenvolvimento
make s3-local      # Incluir LocalStack S3 para testes
make status        # Status dos serviços
make logs          # Ver logs
make test          # Executar testes
make down          # Parar serviços
make clean         # Limpar containers e volumes
```

## 🏗️ Arquitetura

- **Celery**: Processamento assíncrono de tarefas
- **RabbitMQ**: Message broker com filas dedicadas
- **Redis**: Result backend para Celery
- **FFmpeg**: Conversão de vídeo para DASH
- **Supervisor**: Gerenciamento de processos no container
- **AWS S3**: Armazenamento de arquivos de entrada e saída

## 🔧 Configuração

1. Copie `.env.example` para `.env`
2. Configure suas credenciais AWS
3. Execute `make setup`

## 📊 Monitoramento

- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)
- **Logs**: `make logs` ou `make logs-worker`

## 🧪 Desenvolvimento

```bash
# Instalar dependências localmente (para IDE)
make install

# Executar testes
make test

# Formatação de código
make format

# Linting
make lint

# Shell no container
make shell
```