# 🎬 Media Convert Service

A high-performance video conversion service that converts videos to DASH format with multiple bitrates and automatic thumbnail generation for adaptive streaming.

## ✨ Features

- **🎯 Multi-bitrate DASH conversion** (720p, 480p, 360p, 240p)
- **🖼️ Automatic thumbnail generation** with MPD integration
- **⚡ Parallel S3 upload** for optimal performance
- **🔄 Celery-based async processing** with RabbitMQ
- **☸️ EKS-optimized** for m6i.large instances
- **🌐 CDN-ready** with optimized headers
- **📊 Comprehensive logging** and error handling
- **🛠️ Code quality tools** (Ruff, MyPy, Pre-commit)

## 🚀 Quick Start

```bash
# Clone and setup
git clone <repository-url>
cd media-convert
make setup

# Start services
make up

# Test conversion
make test-celery
```

## 🏗️ Architecture

- **Python 3.11** with FastAPI/Celery
- **FFmpeg** for video processing and thumbnails
- **RabbitMQ** for message queuing
- **S3** for storage with parallel uploads
- **Docker** for containerization
- **EKS** for orchestration

## 🛠️ Development

### Code Quality
```bash
make lint          # Run linting with Ruff
make format        # Format code with Ruff
make fix           # Auto-fix code issues
make check         # Run all quality checks
make test          # Run tests
```

### Services
```bash
make up            # Start all services
make down          # Stop all services
make logs          # View logs
make status        # Check status
make clean         # Clean up
```

### Pre-commit Hooks
```bash
# Install pre-commit hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

## ⚙️ Configuration

Key environment variables:
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY`
- `S3_BUCKET` - Target S3 bucket
- `CELERY_BROKER_URL` - RabbitMQ connection
- `MAX_VIDEO_DURATION` - Maximum video length (default: 7200s)
- `FFMPEG_THREADS` - FFmpeg thread count (default: 2)
- `MAX_UPLOAD_WORKERS` - Parallel upload workers (default: 6)

## 📁 Output Structure

```
s3://bucket/videos/video-id/
├── manifest.mpd                 # DASH manifest with thumbnail AdaptationSet
├── input_0init.m4s             # 720p init
├── input_0_000000001.m4s       # 720p segments
├── input_1init.m4s             # 480p init
├── input_1_000000001.m4s       # 480p segments
├── input_2init.m4s             # 360p init
├── input_2_000000001.m4s       # 360p segments
├── input_3init.m4s             # 240p init
├── input_3_000000001.m4s       # 240p segments
├── input_4init.m4s             # Audio init
├── input_4_000000001.m4s       # Audio segments
├── input_thumbnail_0001.jpg    # Thumbnails (every 10s)
├── input_thumbnail_0002.jpg
└── ...
```

## 🎯 DASH Manifest Features

The generated MPD includes:
- **4 video AdaptationSets** (720p, 480p, 360p, 240p)
- **1 audio AdaptationSet** (AAC 96k)
- **1 thumbnail AdaptationSet** (JPEG 320x180)
- **AWS MediaConvert compatible** structure
- **CDN optimized** caching headers

## 🧪 Testing

```bash
# Run all tests
make test

# Run specific test types
pytest tests/unit/
pytest tests/integration/
pytest -m "not slow"  # Skip slow tests
```

## 📊 Monitoring

- **Structured logging** with JSON format
- **System resource monitoring** for EKS
- **Upload progress tracking**
- **Error handling** with detailed context
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)

## 🚢 Deployment

### EKS Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment
kubectl get pods -l app=media-convert-worker
```

### Resource Requirements
- **CPU**: 1.5-2 vCPUs per worker
- **Memory**: 6-7 GB per worker
- **Storage**: 10GB temporary space
- **Network**: Optimized for S3 uploads

## 📄 License

MIT License