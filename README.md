# Media Convert Service

Video conversion service that converts videos to DASH format with multiple bitrates.

## Features

- Multi-bitrate DASH conversion (720p, 480p, 360p, 240p)
- Automatic thumbnail generation
- Parallel S3 upload
- Celery-based async processing with RabbitMQ

## Quick Start

```bash
make setup
make up
make worker
```

## Architecture

- Python 3.11 with <PERSON><PERSON>y
- <PERSON><PERSON><PERSON> for video processing
- RabbitMQ for message queuing
- S3 for storage
- Docker for containerization

## Development

```bash
make logs          # View logs
make clean         # Clean up
```

## Configuration

Environment variables:
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY`
- `S3_BUCKET` - Target S3 bucket
- `CELERY_BROKER_URL` - RabbitMQ connection

## Output Structure

```
s3://bucket/videos/video-id/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```