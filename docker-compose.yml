version: '3.8'

services:
  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: media-convert-rabbitmq
    hostname: rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
      RABBITMQ_DEFAULT_VHOST: media_convert
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./docker/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - media-convert-network

  # Redis Result Backend
  redis:
    image: redis:7.2-alpine
    container_name: media-convert-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - media-convert-network

  # LocalStack for S3 simulation (optional for development)
  localstack:
    image: localstack/localstack:3.0
    container_name: media-convert-localstack
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - localstack_data:/tmp/localstack
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - media-convert-network
    profiles:
      - s3-local  # Only start with --profile s3-local

  # Media Convert Worker (for development)
  media-convert-worker:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    container_name: media-convert-worker
    environment:
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=admin
      - RABBITMQ_PASSWORD=admin123
      - RABBITMQ_VHOST=media_convert
      - REDIS_HOST=redis
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-your-key}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-your-secret}
      - AWS_DEFAULT_REGION=${AWS_REGION:-us-east-1}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET:-media-convert-bucket}
      - CELERY_CONCURRENCY=2
      - LOG_LEVEL=DEBUG
      - ENVIRONMENT=development
    volumes:
      - .:/app
      - ./logs:/var/log/celery
      - ./temp:/tmp/media-convert
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - media-convert-network
    profiles:
      - worker  # Only start with --profile worker

volumes:
  rabbitmq_data:
  redis_data:
  localstack_data:

networks:
  media-convert-network:
    driver: bridge
