#!/usr/bin/env python3
"""
Script de teste para verificar se o sistema está funcionando
"""

import os
import sys
import time
from app.celery_app import celery_app
from app.tasks.notification import send_completion_notification

def test_notification_task():
    """Testa se a task de notificação funciona"""
    print("🧪 Testando task de notificação...")
    
    # Dados de teste
    test_result = {
        "status": "success",
        "task_id": "test-123",
        "message": "Teste de notificação funcionando!"
    }
    
    try:
        # Envia task assíncrona
        result = send_completion_notification.delay(test_result, None)
        print(f"✅ Task enviada com ID: {result.id}")
        
        # Aguarda resultado (timeout de 10 segundos)
        task_result = result.get(timeout=10)
        print(f"✅ Task executada com sucesso: {task_result['message']}")
        return True
        
    except Exception as e:
        print(f"❌ Erro na task: {e}")
        return False

def test_celery_connection():
    """Testa conexão com Celery"""
    print("🧪 Testando conexão Celery...")
    
    try:
        # Testa ping
        inspect = celery_app.control.inspect()
        stats = inspect.ping()
        
        if stats:
            print(f"✅ Celery conectado: {list(stats.keys())}")
            return True
        else:
            print("❌ Nenhum worker respondeu")
            return False
            
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")
        return False

def test_queues():
    """Testa se as filas estão funcionando"""
    print("🧪 Testando filas...")
    
    try:
        inspect = celery_app.control.inspect()
        active_queues = inspect.active_queues()
        
        if active_queues:
            for worker, queues in active_queues.items():
                print(f"✅ Worker {worker}:")
                for queue in queues:
                    print(f"   - Fila: {queue['name']}")
            return True
        else:
            print("❌ Nenhuma fila ativa encontrada")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao verificar filas: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes do sistema Media Convert...")
    print("=" * 50)
    
    tests = [
        ("Conexão Celery", test_celery_connection),
        ("Filas RabbitMQ", test_queues),
        ("Task de Notificação", test_notification_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        success = test_func()
        results.append((test_name, success))
        time.sleep(1)  # Pausa entre testes
    
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES:")
    
    all_passed = True
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
        if not success:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 TODOS OS TESTES PASSARAM! Sistema funcionando corretamente.")
        return 0
    else:
        print("⚠️  ALGUNS TESTES FALHARAM. Verifique a configuração.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
