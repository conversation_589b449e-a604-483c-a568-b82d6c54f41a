apiVersion: apps/v1
kind: Deployment
metadata:
  name: media-convert-worker
  labels:
    app: media-convert-worker
spec:
  replicas: 2  # Start with 2 replicas for m6i.large nodes
  selector:
    matchLabels:
      app: media-convert-worker
  template:
    metadata:
      labels:
        app: media-convert-worker
    spec:
      containers:
      - name: media-convert-worker
        image: your-registry/media-convert:latest
        resources:
          requests:
            memory: "6Gi"      # Reserve 6GB of 8GB available
            cpu: "1.5"         # Reserve 1.5 of 2 vCPUs available
          limits:
            memory: "7Gi"      # Limit to 7GB (leave 1GB for system)
            cpu: "2"           # Limit to 2 vCPUs (full allocation)
        env:
        - name: CELERY_BROKER_URL
          value: "amqp://admin:password@rabbitmq:5672/media_convert"
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        - name: AWS_REGION
          value: "us-east-1"
        - name: S3_BUCKET
          value: "your-media-bucket"
        # EKS-optimized settings
        - name: FFMPEG_THREADS
          value: "2"           # Match CPU limit
        - name: FFMPEG_PRESET
          value: "medium"      # Balance speed/quality for limited CPU
        - name: MAX_UPLOAD_WORKERS
          value: "6"           # Optimized for m6i.large network
        - name: MAX_VIDEO_DURATION
          value: "7200"        # 2 hours max
        volumeMounts:
        - name: tmp-storage
          mountPath: /tmp
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - app.celery_app
            - inspect
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          exec:
            command:
            - celery
            - -A
            - app.celery_app
            - inspect
            - active
          initialDelaySeconds: 15
          periodSeconds: 15
          timeoutSeconds: 5
      volumes:
      - name: tmp-storage
        emptyDir:
          sizeLimit: 10Gi    # Temporary storage for video processing
      nodeSelector:
        node.kubernetes.io/instance-type: m6i.large
      tolerations:
      - key: "media-processing"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
---
apiVersion: v1
kind: Service
metadata:
  name: media-convert-worker-service
spec:
  selector:
    app: media-convert-worker
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: media-convert-worker-pdb
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: media-convert-worker
