[program:celery-worker]
; Celery worker configuration for video conversion

; Command to run the Celery worker
command=celery -A app.celery_app worker --loglevel=info --queues=video_conversion,notifications --concurrency=2 --max-tasks-per-child=1000

; Directory where the command will be executed
directory=/app

; User to run the process as
user=celery

; Number of processes to start
numprocs=1

; Process name template
process_name=%(program_name)s_%(process_num)02d

; Auto start and restart settings
autostart=true
autorestart=true
startsecs=10
startretries=3

; Signal used to kill the process
stopsignal=TERM
stopwaitsecs=600

; Redirect stderr to stdout
redirect_stderr=true

; Log settings
stdout_logfile=/var/log/celery/worker.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=10
stdout_capture_maxbytes=1MB
stdout_events_enabled=false

; Environment variables
environment=
    PYTHONPATH="/app",
    C_FORCE_ROOT="1",
    CELERY_OPTIMIZATION="fair"

; Kill process group on stop
killasgroup=true
stopasgroup=true

; Priority (lower numbers = higher priority)
priority=999
